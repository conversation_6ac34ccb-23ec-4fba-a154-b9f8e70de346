import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { HttpErrorResponse } from '@angular/common/http';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { POS_ENVIRONMENT_CORE } from '../../../../../../app-core/domain/environments';
import { firstValueFrom } from 'rxjs';

describe('PopupRepository', () => {
  let repo: PopupRepository;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PopupRepository,
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            promoApiUrl: 'https://mpromotions.aplazo.net/',
          },
        },
      ],
    });
    repo = TestBed.inject(PopupRepository);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should return available popups on successful API call', async () => {
    // Arrange
    const branchId = 36;
    const mockApiResponse = [
      {
        popupId: 1,
        title: 'Problemas con el ticket',
        content: '¿Tienes problemas para generar el ticket?',
        priority: 1,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
      {
        popupId: 2,
        title: 'Sin señal',
        content: '¿Sin señal?',
        priority: 2,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
    ];

    // Act - Iniciar la petición
    const popupsPromise = firstValueFrom(repo.getAvailablePopups(branchId));

    // Assert - Verificar que se realizó la petición HTTP
    const expectedUrl = `${repo['apiUrl']}/api/v1/popups?branchId=${branchId}`;
    const req = httpMock.expectOne(expectedUrl);

    // Verificar que la petición sea GET
    expect(req.request.method).toBe('GET');

    // Enviar respuesta simulada
    req.flush(mockApiResponse);

    // Obtener el resultado
    const popups = await popupsPromise;

    // Verificar resultados
    expect(popups.length).toBe(2);
    expect(popups[0].popupId).toBe(1);
    expect(popups[0].priority).toBe(1);
    expect(popups[1].popupId).toBe(2);
    expect(popups[1].priority).toBe(2);
  });

  it('should return dummy HTML for popupId 1', async () => {
    // Arrange
    const popupId = 1;
    const mockHtml = '<h2>¿Tienes problemas para generar el ticket?</h2>';

    // Act - Iniciar la petición
    const htmlPromise = firstValueFrom(repo.getPopupHtml(popupId));

    // Assert - Verificar que se realizó la petición HTTP
    const expectedUrl = `${repo['apiUrl']}/api/v1/popups/content/${popupId}`;
    const req = httpMock.expectOne(expectedUrl);

    // Verificar que la petición sea GET
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('text');

    // Enviar respuesta simulada
    req.flush(mockHtml);

    // Obtener el resultado
    const html = await htmlPromise;

    // Verificar resultados
    expect(html).toContain('¿Tienes problemas para generar el ticket?');
  });

  it('should return dummy HTML for popupId 2', async () => {
    // Arrange
    const popupId = 2;
    const mockHtml = '<h2>¿Sin señal?</h2>';

    // Act - Iniciar la petición
    const htmlPromise = firstValueFrom(repo.getPopupHtml(popupId));

    // Assert - Verificar que se realizó la petición HTTP
    const expectedUrl = `${repo['apiUrl']}/api/v1/popups/content/${popupId}`;
    const req = httpMock.expectOne(expectedUrl);

    // Verificar que la petición sea GET
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('text');

    // Enviar respuesta simulada
    req.flush(mockHtml);

    // Obtener el resultado
    const html = await htmlPromise;

    // Verificar resultados
    expect(html).toContain('¿Sin señal?');
  });

  it('should return not found HTML for unknown popup', async () => {
    // Arrange
    const popupId = 999; // ID que no existe
    const errorResponse = new HttpErrorResponse({
      status: 404,
      statusText: 'Not Found',
      error: 'Popup not found',
    });

    // Act - Iniciar la petición
    const htmlPromise = firstValueFrom(repo.getPopupHtml(popupId));

    // Assert - Verificar que se realizó la petición HTTP
    const expectedUrl = `${repo['apiUrl']}/api/v1/popups/content/${popupId}`;
    const req = httpMock.expectOne(expectedUrl);

    // Verificar que la petición sea GET
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('text');

    // Simular un error 404
    req.flush('Popup not found', { status: 404, statusText: 'Not Found' });

    // Obtener el resultado
    const html = await htmlPromise;

    // Verificar resultados
    expect(html).toContain('Popup no encontrado');
  });
});
