import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AplazoPopupComponent } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup.component';
import { DialogRef } from '@ngneat/dialog';
import { StoreService } from 'src/app/core/application/services/store.service';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { BehaviorSubject } from 'rxjs';

describe('AplazoPopupComponent', () => {
  let component: AplazoPopupComponent;
  let fixture: ComponentFixture<AplazoPopupComponent>;
  let storeService: jasmine.SpyObj<StoreService>;
  let popupStorageService: jasmine.SpyObj<PopupStorageService>;
  let dialogRef: jasmine.SpyObj<DialogRef>;

  beforeEach(async () => {
    // Create spies for required services
    storeService = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$: new BehaviorSubject({ id: 123, name: 'Test Branch' }),
    });

    popupStorageService = jasmine.createSpyObj('PopupStorageService', [
      'saveDontShowAgain',
    ]);

    dialogRef = jasmine.createSpyObj('DialogRef', ['close'], {
      data: {
        htmlContent: '<h2>Test Popup</h2><p>Contenido dinámico</p>',
        popupId: 456,
      },
    });

    await TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        { provide: DialogRef, useValue: dialogRef },
        { provide: StoreService, useValue: storeService },
        { provide: PopupStorageService, useValue: popupStorageService },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(AplazoPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render provided HTML content', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<h2>Test Popup</h2>');
    expect(compiled.innerHTML).toContain('<p>Contenido dinámico</p>');
    // Verifica que el DOM incluye el HTML dinámico, no el texto estático anterior
  });

  it('should render fallback if no htmlContent provided', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: jasmine.createSpy('close'),
            data: {},
          },
        },
        { provide: StoreService, useValue: storeService },
        { provide: PopupStorageService, useValue: popupStorageService },
      ],
    }).compileComponents();
    const testFixture = TestBed.createComponent(AplazoPopupComponent);
    testFixture.detectChanges();
    const compiled = testFixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<p>No content</p>');
  });

  it('should update dontShowAgain signal when checkbox changes', () => {
    const checkbox = fixture.nativeElement.querySelector(
      'input[type="checkbox"]'
    ) as HTMLInputElement;

    // Initially unchecked
    expect(component.dontShowAgain()).toBe(false);
    expect(checkbox.checked).toBe(false);

    // Check the checkbox
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));
    fixture.detectChanges();

    expect(component.dontShowAgain()).toBe(true);
    expect(checkbox.checked).toBe(true);

    // Uncheck the checkbox
    checkbox.checked = false;
    checkbox.dispatchEvent(new Event('change'));
    fixture.detectChanges();

    expect(component.dontShowAgain()).toBe(false);
    expect(checkbox.checked).toBe(false);
  });

  it('should save dont show again preference when closing with checkbox checked', () => {
    // Check the checkbox
    const checkbox = fixture.nativeElement.querySelector(
      'input[type="checkbox"]'
    ) as HTMLInputElement;
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));
    fixture.detectChanges();

    // Close the popup
    component.close();

    expect(dialogRef.close).toHaveBeenCalled();
    expect(popupStorageService.saveDontShowAgain).toHaveBeenCalledWith(
      456,
      123
    );
  });

  it('should not save dont show again preference when closing with checkbox unchecked', () => {
    // Ensure checkbox is unchecked
    const checkbox = fixture.nativeElement.querySelector(
      'input[type="checkbox"]'
    ) as HTMLInputElement;
    checkbox.checked = false;
    checkbox.dispatchEvent(new Event('change'));
    fixture.detectChanges();

    // Close the popup
    component.close();

    expect(dialogRef.close).toHaveBeenCalled();
    expect(popupStorageService.saveDontShowAgain).not.toHaveBeenCalled();
  });

  it('should handle missing popupId gracefully', () => {
    // Create a new dialogRef with null popupId
    const newDialogRef = jasmine.createSpyObj('DialogRef', ['close'], {
      data: { htmlContent: '<p>Test</p>', popupId: null },
    });

    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        { provide: DialogRef, useValue: newDialogRef },
        { provide: StoreService, useValue: storeService },
        { provide: PopupStorageService, useValue: popupStorageService },
      ],
    }).compileComponents();

    const testFixture = TestBed.createComponent(AplazoPopupComponent);
    const testComponent = testFixture.componentInstance;
    testFixture.detectChanges();

    // Check the checkbox
    const checkbox = testFixture.nativeElement.querySelector(
      'input[type="checkbox"]'
    ) as HTMLInputElement;
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));
    testFixture.detectChanges();

    // Close the popup
    testComponent.close();

    expect(newDialogRef.close).toHaveBeenCalled();
    expect(popupStorageService.saveDontShowAgain).not.toHaveBeenCalled();
  });
});
