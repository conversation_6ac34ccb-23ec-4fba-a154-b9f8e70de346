import { TestBed } from '@angular/core/testing';
import { AplazoPopupDialogService } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { AplazoPopupComponent } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup.component';
import { DialogService, DialogRef } from '@ngneat/dialog';
import { of } from 'rxjs';

describe('AplazoPopupDialogService', () => {
  let service: AplazoPopupDialogService;
  let dialogServiceSpy: jasmine.SpyObj<DialogService>;

  beforeEach(() => {
    dialogServiceSpy = jasmine.createSpyObj<DialogService>('DialogService', {
      open: { afterClosed$: of('dialog closed') } as DialogRef,
    });

    TestBed.configureTestingModule({
      providers: [
        AplazoPopupDialogService,
        { provide: DialogService, useValue: dialogServiceSpy },
      ],
    });

    service = TestBed.inject(AplazoPopupDialogService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('openPopup', () => {
    const testHtml = '<h2>Test Content</h2>';
    const testPopupId = 123;
    const expectedDialogConfig = {
      data: { htmlContent: testHtml, popupId: testPopupId },
      maxWidth: '600px',
      enableClose: true,
    };

    it('should open a dialog with correct configuration and return its afterClosed$ result', (done: DoneFn) => {
      // Arrange
      const expectedResult = 'dialog was closed successfully';
      // Configuramos el mock para devolver un valor específico para este test
      dialogServiceSpy.open.and.returnValue({
        afterClosed$: of(expectedResult),
      } as DialogRef);

      // Act
      const result$ = service.openPopup(testHtml, testPopupId);

      // Assert: Verificar la llamada al método `open`
      expect(dialogServiceSpy.open).toHaveBeenCalledWith(
        AplazoPopupComponent,
        expectedDialogConfig
      );
      expect(dialogServiceSpy.open).toHaveBeenCalledTimes(1);

      // Assert: Verificar el valor emitido por el observable devuelto
      result$.subscribe({
        next: value => {
          expect(value).toBe(expectedResult);
          done(); // Notifica a Jasmine que el test asíncrono terminó exitosamente
        },
        error: () => {
          fail('The observable should not have thrown an error.');
          done();
        },
      });
    });

    it('should return an observable that emits null and completes if dialog service is not provided', (done: DoneFn) => {
      // Arrange: Usar servicio de TestBed e inyectar, luego forzar dialog a null
      const serviceWithNullDialog = TestBed.inject(AplazoPopupDialogService);
      Object.defineProperty(serviceWithNullDialog, 'dialog', {
        value: null,
        writable: true,
      });

      // Act
      const result$ = serviceWithNullDialog.openPopup(testHtml, testPopupId);

      // Assert: El servicio debería emitir null y luego completarse
      result$.subscribe({
        next: value => {
          expect(value).toBeNull();
          done();
        },
        error: () => fail('Should not throw an error'),
      });
    });
  });
});
