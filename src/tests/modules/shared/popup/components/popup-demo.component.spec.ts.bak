import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { PopupDemoComponent } from '../../../../../app/modules/shared/popup/components/popup-demo.component';
import { PopupTriggerService } from '../../../../../app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupStorageService } from '../../../../../app/modules/shared/popup/application/services/popup-storage.service';
import { PopupRepository } from '../../../../../app/modules/shared/popup/infra/repositories/popup.repository';
import { StoreService } from '../../../../../app/core/application/services/store.service';
import { of } from 'rxjs';

// Declarar los mocks fuera del describe
const popupTriggerServiceMock = jasmine.createSpyObj('PopupTriggerService', [
  'triggerPopupIfNeeded',
  'triggerAfterLogin',
  'triggerAfterPaymentSuccess',
  'shouldShowPopup',
  'getDebugInfo',
]);
const popupStorageServiceMock = jasmine.createSpyObj('PopupStorageService', [
  'wasPopupShownToday',
  'clearRecordsForDate',
  'clearAllRecords',
  'getShownRecords',
  'getDontShowAgainRecords',
  'isDontShowAgain',
  'saveDontShowAgain',
  'clearDontShowAgainRecordsForBranch',
]);
const popupRepositoryMock = jasmine.createSpyObj('PopupRepository', [
  'getAvailablePopups',
]);
const storeServiceMock = jasmine.createSpyObj('StoreService', [], {
  selectedBranch$: of({ id: 36, name: 'Test Branch' }),
});

describe('PopupDemoComponent', () => {
  let fixture: ComponentFixture<PopupDemoComponent>;
  let component: PopupDemoComponent;

  beforeEach(async () => {
    // Reiniciar los spies antes de cada test
    popupTriggerServiceMock.triggerPopupIfNeeded.calls.reset();
    popupTriggerServiceMock.triggerAfterLogin.calls.reset();
    popupTriggerServiceMock.triggerAfterPaymentSuccess.calls.reset();
    popupTriggerServiceMock.shouldShowPopup.calls.reset();
    popupTriggerServiceMock.getDebugInfo.calls.reset();

    popupStorageServiceMock.wasPopupShownToday.calls.reset();
    popupStorageServiceMock.clearRecordsForDate.calls.reset();
    popupStorageServiceMock.clearAllRecords.calls.reset();
    popupStorageServiceMock.getShownRecords.calls.reset();
    popupStorageServiceMock.getDontShowAgainRecords.calls.reset();
    popupStorageServiceMock.isDontShowAgain.calls.reset();
    popupStorageServiceMock.saveDontShowAgain.calls.reset();
    popupStorageServiceMock.clearDontShowAgainRecordsForBranch.calls.reset();

    popupRepositoryMock.getAvailablePopups.calls.reset();

    // Configurar valores de retorno por defecto
    popupRepositoryMock.getAvailablePopups.and.returnValue(of([]));
    popupStorageServiceMock.getShownRecords.and.returnValue([]);
    popupStorageServiceMock.getDontShowAgainRecords.and.returnValue([]);
    popupStorageServiceMock.wasPopupShownToday.and.returnValue(false);
    popupStorageServiceMock.isDontShowAgain.and.returnValue(false);
    popupTriggerServiceMock.triggerPopupIfNeeded.and.returnValue(of(true));
    popupTriggerServiceMock.triggerAfterLogin.and.returnValue(of(true));
    popupTriggerServiceMock.triggerAfterPaymentSuccess.and.returnValue(
      of(true)
    );
    popupTriggerServiceMock.shouldShowPopup.and.returnValue(of(true));
    popupTriggerServiceMock.getDebugInfo.and.returnValue(
      of({
        availablePopups: [],
        shownToday: [],
        dontShowAgain: [],
        eligiblePopups: [],
      })
    );

    await TestBed.configureTestingModule({
      imports: [PopupDemoComponent],
      providers: [
        { provide: PopupTriggerService, useValue: popupTriggerServiceMock },
        { provide: PopupStorageService, useValue: popupStorageServiceMock },
        { provide: PopupRepository, useValue: popupRepositoryMock },
        { provide: StoreService, useValue: storeServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with current branch ID', fakeAsync(() => {
    fixture.detectChanges();
    tick();
    expect(component.currentBranchId()).toBe('36');
    expect(component.config.branchId).toBe('36');
  }));

  it('should load available popups on initialization', fakeAsync(() => {
    const mockPopups = [
      {
        popupId: 'POPUP-001',
        priority: 1,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
    ];
    popupRepositoryMock.getAvailablePopups.and.returnValue(of(mockPopups));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    expect(component.availablePopups()).toEqual(mockPopups);
  }));

  it('should trigger popup manually', fakeAsync(() => {
    popupTriggerServiceMock.triggerPopupIfNeeded.and.returnValue(of(true));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    component.config.customHtml = '';
    component.triggerPopup();
    tick();
    expect(popupTriggerServiceMock.triggerPopupIfNeeded).toHaveBeenCalledWith({
      branchId: '36',
    });
    expect(component.lastResult()).toBeTruthy();
    expect(component.lastResult().success).toBe(true);
    expect(component.lastResult().triggerType).toBe('manual');
  }));

  it('should trigger popup after login', fakeAsync(() => {
    component.config.triggerType = 'login';
    fixture.detectChanges();
    tick();
    component.triggerPopup();
    tick();
    expect(popupTriggerServiceMock.triggerAfterLogin).toHaveBeenCalledWith(
      '36'
    );
    expect(component.lastResult().triggerType).toBe('login');
  }));

  it('should trigger popup after payment', fakeAsync(() => {
    component.config.triggerType = 'payment';
    fixture.detectChanges();
    tick();
    component.triggerPopup();
    tick();
    expect(
      popupTriggerServiceMock.triggerAfterPaymentSuccess
    ).toHaveBeenCalledWith('36');
    expect(component.lastResult().triggerType).toBe('payment');
  }));

  it('should check popup status', fakeAsync(() => {
    fixture.detectChanges();
    tick();
    component.checkPopupStatus();
    tick();
    expect(popupTriggerServiceMock.shouldShowPopup).toHaveBeenCalledWith({
      branchId: '36',
    });
    expect(component.lastResult().action).toBe('status_check');
    expect(component.lastResult().shouldShow).toBe(true);
  }));

  it('should clear today records', fakeAsync(() => {
    fixture.detectChanges();
    tick();
    component.clearTodayRecords();
    tick();
    expect(popupStorageServiceMock.clearRecordsForDate).toHaveBeenCalledWith(
      component.today()
    );
    expect(component.lastResult().action).toBe('clear_today');
  }));

  it('should clear all records', fakeAsync(() => {
    fixture.detectChanges();
    tick();
    component.clearAllRecords();
    tick();
    expect(popupStorageServiceMock.clearAllRecords).toHaveBeenCalled();
    expect(component.lastResult().action).toBe('clear_all');
  }));

  it('should handle errors during popup trigger', fakeAsync(() => {
    popupTriggerServiceMock.triggerPopupIfNeeded.and.returnValue(of(false));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    component.config.customHtml = '';
    component.triggerPopup();
    tick();
    expect(component.lastResult().success).toBe(false);
  }));

  it('should update shown today count', fakeAsync(() => {
    const mockPopups = [
      {
        popupId: 'POPUP-001',
        priority: 1,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
      {
        popupId: 'POPUP-002',
        priority: 2,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
    ];
    popupRepositoryMock.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorageServiceMock.wasPopupShownToday.and.callFake(
      (popupId: string) => popupId === 'POPUP-001'
    );
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    (component as any).updateShownToday();
    expect(component.shownToday()).toBe(1);
  }));
});
