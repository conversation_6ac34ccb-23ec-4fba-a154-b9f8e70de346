import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

describe('PopupId', () => {
  describe('constructor', () => {
    it('should create PopupId with valid number value', () => {
      const id = new PopupId(123);
      expect(id.getValue()).toBe(123);
    });

    it('should throw error when creating PopupId with zero', () => {
      expect(() => new PopupId(0)).toThrowError(
        'Invalid PopupId: ID must be a positive number'
      );
    });

    it('should throw error when creating PopupId with negative number', () => {
      expect(() => new PopupId(-1)).toThrowError(
        'Invalid PopupId: ID must be a positive number'
      );
    });

    it('should throw error when creating PopupId with null', () => {
      expect(() => new PopupId(null as unknown as number)).toThrowError(
        'Invalid PopupId: ID must be a positive number'
      );
    });

    it('should throw error when creating PopupId with undefined', () => {
      expect(() => new PopupId(undefined as unknown as number)).toThrowError(
        'Invalid PopupId: ID must be a positive number'
      );
    });
  });

  describe('getValue', () => {
    it('should return the string value', () => {
      const id = new PopupId(456);
      expect(id.getValue()).toBe(456);
    });
  });

  describe('equals', () => {
    it('should return true when comparing identical PopupIds', () => {
      const id1 = new PopupId(123);
      const id2 = new PopupId(123);
      expect(id1.equals(id2)).toBe(true);
    });

    it('should return false when comparing different PopupIds', () => {
      const id1 = new PopupId(123);
      const id2 = new PopupId(456);
      expect(id1.equals(id2)).toBe(false);
    });
  });

  describe('toString', () => {
    it('should return the string representation', () => {
      const id = new PopupId(789);
      expect(id.toString()).toBe('789');
    });
  });

  describe('fromString', () => {
    it('should create PopupId from number', () => {
      const id = new PopupId(123);
      expect(id).toBeInstanceOf(PopupId);
      expect(id.getValue()).toBe(123);
    });

    it('should throw error when creating from invalid number', () => {
      expect(() => new PopupId(0)).toThrowError(
        'Invalid PopupId: ID must be a positive number'
      );
    });
  });

  describe('random', () => {
    it('should create PopupId with random positive number', () => {
      const id = PopupId.random();
      expect(id.getValue()).toBeGreaterThan(0);
    });

    it('should create different PopupIds on multiple calls', () => {
      const id1 = PopupId.random();
      const id2 = PopupId.random();
      expect(id1.equals(id2)).toBe(false);
    });
  });
});
