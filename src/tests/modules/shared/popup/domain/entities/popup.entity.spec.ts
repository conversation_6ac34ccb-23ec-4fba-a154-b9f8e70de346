import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';
import { PopupEntity } from 'src/app/modules/shared/popup/domain/entities/popup.entity';

describe('PopupEntity', () => {
  let popupId: PopupId;
  let validPopup: PopupEntity;
  let expiredPopup: PopupEntity;
  let futureStartPopup: PopupEntity;

  beforeEach(() => {
    popupId = new PopupId(1);

    // Create a future expiration date (1 year from now)
    const futureExpirationDate = new Date();
    futureExpirationDate.setFullYear(futureExpirationDate.getFullYear() + 1);

    // Create a past start date (1 year ago)
    const pastStartDate = new Date();
    pastStartDate.setFullYear(pastStartDate.getFullYear() - 1);

    validPopup = new PopupEntity(
      popupId,
      'Test Title',
      'Test Content',
      1,
      true,
      new Date('2024-01-01'),
      pastStartDate,
      futureExpirationDate
    );

    // Create a past expiration date (1 year ago)
    const pastExpirationDate = new Date();
    pastExpirationDate.setFullYear(pastExpirationDate.getFullYear() - 1);

    expiredPopup = new PopupEntity(
      new PopupId(99),
      'Expired Title',
      'Expired Content',
      1,
      true,
      new Date('2024-01-01'),
      pastStartDate,
      pastExpirationDate
    );

    // Create a future start date (1 year from now)
    const futureStartDate = new Date();
    futureStartDate.setFullYear(futureStartDate.getFullYear() + 1);

    futureStartPopup = new PopupEntity(
      new PopupId(100),
      'Future Start Title',
      'Future Start Content',
      1,
      true,
      new Date('2024-01-01'),
      futureStartDate,
      futureExpirationDate
    );
  });

  describe('constructor', () => {
    it('should create PopupEntity with valid parameters', () => {
      expect(validPopup.id).toBe(popupId);
      expect(validPopup.title).toBe('Test Title');
      expect(validPopup.content).toBe('Test Content');
      expect(validPopup.priority).toBe(1);
      expect(validPopup.isActive).toBe(true);
    });

    it('should create PopupEntity without start and expiration date', () => {
      const popupWithoutDates = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );

      expect(popupWithoutDates.startAt).toBeUndefined();
      expect(popupWithoutDates.expiresAt).toBeUndefined();
    });
  });

  describe('isValid', () => {
    it('should return true for valid popup', () => {
      expect(validPopup.isValid()).toBe(true);
    });

    it('should return false for popup with empty title', () => {
      const invalidPopup = new PopupEntity(
        popupId,
        '',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(invalidPopup.isValid()).toBe(false);
    });

    it('should return false for popup with empty content', () => {
      const invalidPopup = new PopupEntity(
        popupId,
        'Test Title',
        '',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(invalidPopup.isValid()).toBe(false);
    });

    it('should return false for popup with negative priority', () => {
      const invalidPopup = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        -1,
        true,
        new Date('2024-01-01')
      );
      expect(invalidPopup.isValid()).toBe(false);
    });

    it('should return false for inactive popup', () => {
      const invalidPopup = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        false,
        new Date('2024-01-01')
      );
      expect(invalidPopup.isValid()).toBe(false);
    });
  });

  describe('canBeDisplayed', () => {
    it('should return true for valid, started, and non-expired popup', () => {
      expect(validPopup.canBeDisplayed()).toBe(true);
    });

    it('should return false for expired popup', () => {
      expect(expiredPopup.canBeDisplayed()).toBe(false);
    });

    it('should return false for popup that has not started', () => {
      expect(futureStartPopup.canBeDisplayed()).toBe(false);
    });

    it('should return false for invalid popup', () => {
      const invalidPopup = new PopupEntity(
        popupId,
        '',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(invalidPopup.canBeDisplayed()).toBe(false);
    });
  });

  describe('isExpired', () => {
    it('should return false for non-expired popup', () => {
      expect(validPopup.isExpired()).toBe(false);
    });

    it('should return true for expired popup', () => {
      expect(expiredPopup.isExpired()).toBe(true);
    });

    it('should return false for popup without expiration date', () => {
      const popupWithoutExpiration = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(popupWithoutExpiration.isExpired()).toBe(false);
    });
  });

  describe('hasStarted', () => {
    it('should return true for popup that has started', () => {
      expect(validPopup.hasStarted()).toBe(true);
    });

    it('should return false for popup with future start date', () => {
      expect(futureStartPopup.hasStarted()).toBe(false);
    });

    it('should return true for popup without start date', () => {
      const popupWithoutStart = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(popupWithoutStart.hasStarted()).toBe(true);
    });
  });

  describe('getTimeUntilExpiration', () => {
    it('should return time until expiration for valid popup', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1); // Tomorrow

      const popup = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        true,
        new Date('2024-01-01'),
        undefined, // startAt (optional)
        futureDate // expiresAt
      );

      const timeUntilExpiration = popup.getTimeUntilExpiration();
      expect(timeUntilExpiration).toBeGreaterThan(0);
    });

    it('should return 0 for expired popup', () => {
      const timeUntilExpiration = expiredPopup.getTimeUntilExpiration();
      expect(timeUntilExpiration).toBe(0);
    });

    it('should return null for popup without expiration date', () => {
      const popupWithoutExpiration = new PopupEntity(
        popupId,
        'Test Title',
        'Test Content',
        1,
        true,
        new Date('2024-01-01')
      );
      expect(popupWithoutExpiration.getTimeUntilExpiration()).toBeNull();
    });
  });

  describe('immutable methods', () => {
    it('should create new instance with updated title', () => {
      const newPopup = validPopup.withTitle('New Title');
      expect(newPopup.title).toBe('New Title');
      expect(newPopup).not.toBe(validPopup); // Different instance
      expect(validPopup.title).toBe('Test Title'); // Original unchanged
    });

    it('should create new instance with updated content', () => {
      const newPopup = validPopup.withContent('New Content');
      expect(newPopup.content).toBe('New Content');
      expect(newPopup).not.toBe(validPopup);
      expect(validPopup.content).toBe('Test Content');
    });

    it('should create new instance with updated priority', () => {
      const newPopup = validPopup.withPriority(5);
      expect(newPopup.priority).toBe(5);
      expect(newPopup).not.toBe(validPopup);
      expect(validPopup.priority).toBe(1);
    });

    it('should create new instance with updated active status', () => {
      const newPopup = validPopup.withActiveStatus(false);
      expect(newPopup.isActive).toBe(false);
      expect(newPopup).not.toBe(validPopup);
      expect(validPopup.isActive).toBe(true);
    });

    it('should create new instance with updated start date', () => {
      const newStartDate = new Date('2025-01-01');
      const newPopup = validPopup.withStartDate(newStartDate);
      expect(newPopup.startAt).toBe(newStartDate);
      expect(newPopup).not.toBe(validPopup);
      expect(validPopup.startAt).not.toBe(newStartDate);
    });
  });

  describe('comparePriority', () => {
    it('should return negative when this popup has higher priority', () => {
      const highPriorityPopup = new PopupEntity(
        new PopupId(10),
        'High Priority',
        'Content',
        5,
        true,
        new Date('2024-01-01')
      );

      const lowPriorityPopup = new PopupEntity(
        new PopupId(20),
        'Low Priority',
        'Content',
        1,
        true,
        new Date('2024-01-01')
      );

      const result = highPriorityPopup.comparePriority(lowPriorityPopup);
      expect(result).toBeLessThan(0);
    });

    it('should return positive when this popup has lower priority', () => {
      const lowPriorityPopup = new PopupEntity(
        new PopupId(20),
        'Low Priority',
        'Content',
        1,
        true,
        new Date('2024-01-01')
      );

      const highPriorityPopup = new PopupEntity(
        new PopupId(10),
        'High Priority',
        'Content',
        5,
        true,
        new Date('2024-01-01')
      );

      const result = lowPriorityPopup.comparePriority(highPriorityPopup);
      expect(result).toBeGreaterThan(0);
    });

    it('should return 0 when popups have same priority', () => {
      const popup1 = new PopupEntity(
        new PopupId(1),
        'Popup 1',
        'Content',
        3,
        true,
        new Date('2024-01-01')
      );

      const popup2 = new PopupEntity(
        new PopupId(2),
        'Popup 2',
        'Content',
        3,
        true,
        new Date('2024-01-01')
      );

      const result = popup1.comparePriority(popup2);
      expect(result).toBe(0);
    });
  });

  describe('toString', () => {
    it('should return string representation', () => {
      const result = validPopup.toString();
      expect(result).toContain('Popup(id=1');
      expect(result).toContain('title="Test Title"');
      expect(result).toContain('priority=1');
    });
  });
});
