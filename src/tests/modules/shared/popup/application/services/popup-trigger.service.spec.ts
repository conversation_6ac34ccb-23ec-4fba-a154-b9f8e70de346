import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import {
  PopupTriggerService,
  PopupTriggerConfig,
} from 'src/app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { PopupEntity } from 'src/app/modules/shared/popup/domain/entities/popup.entity';
import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

describe('PopupTriggerService', () => {
  let service: PopupTriggerService;
  let popupRepository: jasmine.SpyObj<PopupRepository>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;

  const mockPopups: PopupEntity[] = [
    new PopupEntity(
      new PopupId(1),
      'Problemas con el ticket',
      '¿Tienes problemas para generar el ticket?',
      1,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
    new PopupEntity(
      new PopupId(2),
      'Sin señal',
      '¿Sin señal?',
      2,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
  ];

  beforeEach(() => {
    const popupRepositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
    ]);
    const popupStorageSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'cleanupOldRecords',
      'isDontShowAgain',
    ]);
    const showPopupUseCaseSpy = jasmine.createSpyObj('ShowPopupUseCase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      providers: [
        PopupTriggerService,
        { provide: PopupRepository, useValue: popupRepositorySpy },
        { provide: PopupStorageService, useValue: popupStorageSpy },
        { provide: ShowPopupUseCase, useValue: showPopupUseCaseSpy },
      ],
    });

    service = TestBed.inject(PopupTriggerService);
    popupRepository = TestBed.inject(
      PopupRepository
    ) as jasmine.SpyObj<PopupRepository>;
    popupStorage = TestBed.inject(
      PopupStorageService
    ) as jasmine.SpyObj<PopupStorageService>;
    showPopupUseCase = TestBed.inject(
      ShowPopupUseCase
    ) as jasmine.SpyObj<ShowPopupUseCase>;

    // Default mock setup
    popupStorage.isDontShowAgain.and.returnValue(false);
  });

  it('should return false when no popups are available', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(36);
  }));

  it('should return true when popup is available and not shown today and not marked as dont show again', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isDontShowAgain.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      2,
      36,
      jasmine.any(String)
    );
    expect(popupStorage.isDontShowAgain).toHaveBeenCalledWith(2, 36);
  }));

  it('should return false when all popups have been shown today', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(true);
    popupStorage.isDontShowAgain.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
  }));

  it('should return false when popup is marked as dont show again', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isDontShowAgain.and.returnValue(true);

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(popupStorage.isDontShowAgain).toHaveBeenCalledWith(2, 36);
  }));

  it('should prioritize popups by priority (higher number = higher priority)', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.callFake((popupId: number) => {
      return popupId === 1; // POPUP-001 ya mostrado, POPUP-002 no
    });
    popupStorage.isDontShowAgain.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      2,
      36,
      jasmine.any(String)
    );
    expect(popupStorage.isDontShowAgain).toHaveBeenCalledWith(2, 36);
  }));

  it('should fallback to next popup when first is marked as dont show again', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isDontShowAgain.and.callFake((popupId: number) => {
      return popupId === 2; // POPUP-002 marcado como dont show again
    });

    let result = false;
    service.shouldShowPopup({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(popupStorage.isDontShowAgain).toHaveBeenCalledWith(2, 36);
    expect(popupStorage.isDontShowAgain).toHaveBeenCalledWith(1, 36);
  }));

  it('should not trigger popup when shouldShowPopup returns false', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    service.triggerPopupIfNeeded({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should trigger popup when shouldShowPopup returns true', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isDontShowAgain.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: true, popupShown: mockPopups[0] })
    );

    let result = false;
    service.triggerPopupIfNeeded({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should not mark as shown when popup execution fails', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isDontShowAgain.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: false, reason: 'Error' })
    );

    let result = false;
    service.triggerPopupIfNeeded({ branchId: 36 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
  }));

  it('should call triggerPopupIfNeeded with login configuration', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    service.triggerAfterLogin(36).subscribe();
    tick();

    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(36);
  }));

  it('should call triggerPopupIfNeeded with payment configuration', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    service.triggerAfterPaymentSuccess(36).subscribe();
    tick();

    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(36);
  }));

  it('should call cleanupOldRecords on popupStorage', () => {
    service.cleanupOldRecords();

    expect(popupStorage.cleanupOldRecords).toHaveBeenCalled();
  });

  it('should return debug info with popup status', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.callFake((popupId: number) => {
      return popupId === 1;
    });
    popupStorage.isDontShowAgain.and.callFake((popupId: number) => {
      return popupId === 2;
    });

    let debugInfo: any = null;
    service.getDebugInfo(36).subscribe(info => {
      debugInfo = info;
    });
    tick();

    expect(debugInfo).toBeTruthy();
    expect(debugInfo.availablePopups).toEqual(
      jasmine.arrayWithExactContents(mockPopups)
    );
    expect([...debugInfo.shownToday, ...debugInfo.dontShowAgain]).toEqual(
      jasmine.arrayWithExactContents(mockPopups)
    );
    expect(debugInfo.eligiblePopups).toEqual([]);
  }));
});
