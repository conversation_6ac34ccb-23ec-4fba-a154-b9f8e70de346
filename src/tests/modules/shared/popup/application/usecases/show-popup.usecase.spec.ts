import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of, Observable } from 'rxjs';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import {
  PopupRepository,
  PopupMeta,
} from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { AplazoPopupDialogService } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';
import { PopupEntity } from 'src/app/modules/shared/popup/domain/entities/popup.entity';
import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

describe('ShowPopupUseCase', () => {
  const branchId = 1001;
  const mockBranch = { id: branchId, name: 'Test Branch' };
  const mockPopup1 = PopupId.fromNumber(1);
  const mockPopup2 = PopupId.fromNumber(2);
  const mockPopups: PopupEntity[] = [
    new PopupEntity(
      mockPopup1,
      'Problemas con el ticket',
      '¿Tienes problemas para generar el ticket?',
      1,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
    new PopupEntity(
      mockPopup2,
      'Sin señal',
      '¿Sin señal?',
      2,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
  ];

  function setupTestBed({
    selectedBranch$ = of(mockBranch),
  }: {
    selectedBranch$?: Observable<{ id: number; name: string } | null>;
  } = {}) {
    const popupRepoSpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
      'getPopupHtml',
    ]);
    const dialogServiceSpy = jasmine.createSpyObj('AplazoPopupDialogService', [
      'openPopup',
    ]);
    const storageServiceSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'isDontShowAgain',
    ]);
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$,
    });

    TestBed.configureTestingModule({
      providers: [
        ShowPopupUseCase,
        { provide: PopupRepository, useValue: popupRepoSpy },
        { provide: AplazoPopupDialogService, useValue: dialogServiceSpy },
        { provide: PopupStorageService, useValue: storageServiceSpy },
        { provide: StoreService, useValue: storeServiceSpy },
      ],
    });

    return {
      useCase: TestBed.inject(ShowPopupUseCase),
      popupRepo: TestBed.inject(
        PopupRepository
      ) as jasmine.SpyObj<PopupRepository>,
      dialogService: TestBed.inject(
        AplazoPopupDialogService
      ) as jasmine.SpyObj<AplazoPopupDialogService>,
      storageService: TestBed.inject(
        PopupStorageService
      ) as jasmine.SpyObj<PopupStorageService>,
      storeService: TestBed.inject(
        StoreService
      ) as jasmine.SpyObj<StoreService>,
    };
  }

  it('should return success when popup is available and shown', fakeAsync(() => {
    const { useCase, popupRepo, storageService, dialogService } =
      setupTestBed();
    const today = new Date().toISOString().split('T')[0];
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
    storageService.wasPopupShownToday.and.returnValue(false);
    storageService.isDontShowAgain.and.returnValue(false);
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(true);
    expect(result!.popupShown).toEqual(mockPopups[1]); // Highest priority
    expect(storageService.markPopupAsShown).toHaveBeenCalledWith(
      2,
      branchId,
      today
    );
    expect(dialogService.openPopup).toHaveBeenCalledWith(
      '<h2>Test Popup</h2>',
      2
    );
  }));

  it('should return failure when no branch is selected', fakeAsync(() => {
    const { useCase } = setupTestBed({ selectedBranch$: of(null) });
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe('No branch selected');
  }));

  it('should return failure when no popups are available', fakeAsync(() => {
    const { useCase, popupRepo } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of([]));
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe('No popups available');
  }));

  it('should return failure when all popups already shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(true);
    storageService.isDontShowAgain.and.returnValue(false);
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe(
      'All popups already shown today or marked as dont show again'
    );
  }));

  it('should filter out already shown popups and show highest priority remaining', fakeAsync(() => {
    const { useCase, popupRepo, storageService, dialogService } =
      setupTestBed();
    const today = new Date().toISOString().split('T')[0];
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
    storageService.wasPopupShownToday.and.callFake((popupId: number) => {
      return popupId === 2;
    });
    storageService.isDontShowAgain.and.returnValue(false);
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(true);
    expect(result!.popupShown).toEqual(mockPopups[0]); // POPUP-001 has highest priority among remaining
    expect(storageService.markPopupAsShown).toHaveBeenCalledWith(
      1,
      branchId,
      today
    );
    expect(dialogService.openPopup).toHaveBeenCalledWith(
      '<h2>Test Popup</h2>',
      1
    );
  }));

  it('should return true when popups are available and not shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(false);
    storageService.isDontShowAgain.and.returnValue(false);
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(true);
  }));

  it('should return false when no popups are available', fakeAsync(() => {
    const { useCase, popupRepo } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of([]));
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));

  it('should return false when all popups already shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(true);
    storageService.isDontShowAgain.and.returnValue(false);
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));

  it('should return false when no branch is selected', fakeAsync(() => {
    const { useCase } = setupTestBed({ selectedBranch$: of(null) });
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));
});
