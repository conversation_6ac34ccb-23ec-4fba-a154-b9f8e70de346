import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  CheckPaymentSuccessUseCase,
  PaymentSuccessEvent,
} from 'src/app/modules/shared/popup/application/usecases/check-payment-success.usecase';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { PosOfflineService } from 'src/app/core/services/pos-offline.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { BehaviorSubject, of } from 'rxjs';
import { Order } from 'src/app/core/domain/order.interface';

describe('CheckPaymentSuccessUseCase', () => {
  let useCase: CheckPaymentSuccessUseCase;
  let ordersService: jasmine.SpyObj<OrdersWithSocketService>;
  let posOfflineService: jasmine.SpyObj<PosOfflineService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;

  const mockOrders = new BehaviorSubject<Order[]>([]);

  const mockPaidOrder: Order = {
    id: 1,
    loanId: 123,
    branchId: 456,
    status: 'APROBADO',
    createdAt: new Date(),
    updatedAt: new Date(),
    DeletedAt: null,
    date: new Date(),
    price: 100,
    url: 'test-url',
    merchantId: 789,
    products: [],
    branch: { id: 456 },
  };

  beforeEach(() => {
    ordersService = jasmine.createSpyObj('OrdersWithSocketService', [], {
      orders$: mockOrders,
    });

    posOfflineService = jasmine.createSpyObj('PosOfflineService', [
      'checkOrderStatus',
    ]);

    showPopupUseCase = jasmine.createSpyObj('ShowPopupUseCase', ['execute']);
    showPopupUseCase.execute.and.callFake(() => of({ success: true }));

    popupStorage = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
    ]);
    popupStorage.wasPopupShownToday.and.returnValue(false);

    TestBed.configureTestingModule({
      providers: [
        CheckPaymentSuccessUseCase,
        { provide: OrdersWithSocketService, useValue: ordersService },
        { provide: PosOfflineService, useValue: posOfflineService },
        { provide: ShowPopupUseCase, useValue: showPopupUseCase },
        { provide: PopupStorageService, useValue: popupStorage },
      ],
    });

    useCase = TestBed.inject(CheckPaymentSuccessUseCase);
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should start monitoring for successful payments', () => {
    useCase.startMonitoring();
    expect(ordersService.orders$).toBeDefined();
  });

  it('should trigger popup after detecting first paid order of the day', fakeAsync(() => {
    useCase.resetDailyTracking();
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));
    useCase.startMonitoring();

    // Emit a paid order
    mockOrders.next([mockPaidOrder]);

    // Wait for the 7-second timer
    tick(7000);

    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should not trigger popup for non-paid orders', fakeAsync(() => {
    const nonPaidOrder: Order = {
      ...mockPaidOrder,
      status: 'Pendiente',
    };

    useCase.startMonitoring();
    mockOrders.next([nonPaidOrder]);

    tick(7000);

    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should not trigger popup if already shown today', fakeAsync(() => {
    popupStorage.wasPopupShownToday.and.returnValue(true);

    useCase.startMonitoring();
    mockOrders.next([mockPaidOrder]);

    tick(7000);

    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should only trigger once per branch per day', fakeAsync(() => {
    // Reset any internal tracking state before the test
    useCase.resetDailyTracking();
    // Mock wasPopupShownToday to return false for the first call, and true for the second
    let callCount = 0;
    popupStorage.wasPopupShownToday.and.callFake(() => {
      callCount++;
      return callCount > 1;
    });
    showPopupUseCase.execute.and.returnValue(of({ success: true }));

    useCase.startMonitoring();

    // First paid order
    mockOrders.next([mockPaidOrder]);
    tick(7000);

    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);

    // Second paid order (should not trigger again)
    const secondPaidOrder: Order = {
      ...mockPaidOrder,
      loanId: 789,
    };
    mockOrders.next([mockPaidOrder, secondPaidOrder]);
    tick(7000);

    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
  }));

  it('should handle multiple branches correctly', fakeAsync(() => {
    useCase.resetDailyTracking();
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));
    const branch1Order: Order = {
      ...mockPaidOrder,
      branchId: 456,
      branch: { id: 456 },
    };

    const branch2Order: Order = {
      ...mockPaidOrder,
      loanId: 789,
      branchId: 789,
      branch: { id: 789 },
    };

    useCase.startMonitoring();
    mockOrders.next([branch1Order, branch2Order]);

    tick(7000);

    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(2);
  }));

  it('should reset daily tracking', () => {
    useCase.resetDailyTracking();
    // This method clears internal state, so we just verify it doesn't throw
    expect().nothing();
  });

  it('should handle manual order status check', fakeAsync(() => {
    posOfflineService.checkOrderStatus.and.callFake(() => {
      return of({ content: mockPaidOrder, error: false, code: 200 });
    });

    let result: PaymentSuccessEvent | null = null;
    useCase.checkOrderStatus(123).subscribe(event => {
      result = event;
    });

    tick(7000);

    expect(result).toBeTruthy();
    expect(result!.status).toBe('APROBADO');
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should handle errors gracefully', fakeAsync(() => {
    spyOn(console, 'error');
    useCase.startMonitoring();
    mockOrders.error(new Error('Test error'));
    tick(1000);
    expect(console.error).toHaveBeenCalled();
  }));
});
