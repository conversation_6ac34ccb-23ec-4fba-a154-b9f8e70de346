import {
  TestBed,
  fakeAsync,
  tick,
  flushMicrotasks,
  discardPeriodicTasks,
} from '@angular/core/testing';
import {
  CheckPaymentSuccessUseCase,
  PaymentSuccessEvent,
} from 'src/app/modules/shared/popup/application/usecases/check-payment-success.usecase';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { PosOfflineService } from 'src/app/core/services/pos-offline.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { BehaviorSubject, of } from 'rxjs';
import { Order } from 'src/app/core/domain/order.interface';
import { StoreService } from 'src/app/core/application/services/store.service';

describe('CheckPaymentSuccessUseCase', () => {
  let useCase: CheckPaymentSuccessUseCase;
  let ordersService: jasmine.SpyObj<OrdersWithSocketService>;
  let posOfflineService: jasmine.SpyObj<PosOfflineService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let storeService: jasmine.SpyObj<StoreService>;

  const mockOrders = new BehaviorSubject<Order[]>([]);

  const mockPaidOrder: Order = {
    id: 1,
    loanId: 123,
    branchId: 456,
    status: 'Pagado',
    createdAt: new Date(),
    updatedAt: new Date(),
    DeletedAt: null,
    date: new Date(),
    price: 100,
    url: 'test-url',
    merchantId: 789,
    products: [],
    branch: { id: 456 },
  };

  beforeEach(() => {
    // Reset the mock orders subject to ensure clean state
    mockOrders.next([]);

    ordersService = jasmine.createSpyObj('OrdersWithSocketService', [], {
      orders$: mockOrders,
    });

    posOfflineService = jasmine.createSpyObj('PosOfflineService', [
      'checkOrderStatus',
    ]);

    showPopupUseCase = jasmine.createSpyObj('ShowPopupUseCase', ['execute']);
    showPopupUseCase.execute.and.callFake(() => of({ success: true }));

    popupStorage = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
    ]);
    popupStorage.wasPopupShownToday.and.returnValue(false);

    storeService = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$: of({ id: 456, name: 'Test Branch' }),
    });

    TestBed.configureTestingModule({
      providers: [
        CheckPaymentSuccessUseCase,
        { provide: OrdersWithSocketService, useValue: ordersService },
        { provide: PosOfflineService, useValue: posOfflineService },
        { provide: ShowPopupUseCase, useValue: showPopupUseCase },
        { provide: PopupStorageService, useValue: popupStorage },
        { provide: StoreService, useValue: storeService },
      ],
    });

    useCase = TestBed.inject(CheckPaymentSuccessUseCase);
    // Ensure clean state for each test
    useCase.resetDailyTracking();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should start monitoring for successful payments', () => {
    useCase.startMonitoring();
    expect(ordersService.orders$).toBeDefined();
  });

  it('should trigger popup after detecting first paid order of the day', fakeAsync(() => {
    // Setup: No popup shown today, first payment
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));

    useCase.startMonitoring();

    // Act: Emit first paid order of the day
    mockOrders.next([mockPaidOrder]);

    // Wait for the 7-second timer
    tick(7000);
    flushMicrotasks();

    // Assert: Verify side effects - popup should be triggered
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      1,
      456,
      jasmine.any(String)
    );
  }));

  it('should not trigger popup for non-paid orders', fakeAsync(() => {
    const nonPaidOrder: Order = {
      ...mockPaidOrder,
      status: 'Pendiente',
    };

    useCase.startMonitoring();

    // Act: Emit non-paid order
    mockOrders.next([nonPaidOrder]);
    tick(7000);
    flushMicrotasks();

    // Assert: Verify no side effects - no popup triggered
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should not trigger popup if already shown today', fakeAsync(() => {
    // Setup: Popup already shown today
    popupStorage.wasPopupShownToday.and.returnValue(true);

    useCase.startMonitoring();

    // Act: Emit paid order
    mockOrders.next([mockPaidOrder]);
    tick(7000);
    flushMicrotasks();

    // Assert: Verify no side effects - no popup triggered because already shown
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should only trigger once per branch per day', fakeAsync(() => {
    // Setup: No popup shown today, first payment scenario
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));

    useCase.startMonitoring();

    // Act: First paid order (first installment)
    mockOrders.next([mockPaidOrder]);
    tick(7000);
    flushMicrotasks();

    // Assert: Verify popup triggered for first payment
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);

    // Act: Second paid order for same branch (should not trigger again)
    const secondPaidOrder: Order = {
      ...mockPaidOrder,
      loanId: 789,
    };
    mockOrders.next([mockPaidOrder, secondPaidOrder]);
    tick(7000);
    flushMicrotasks();

    // Assert: Verify popup was not triggered again (still only once)
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
  }));

  it('should handle multiple branches correctly', fakeAsync(() => {
    // Setup: No popup shown today, multiple branches with first payments
    // NOTE: Current implementation shows popup for each branch, but acceptance criteria
    // suggests only current branch should show popup. This test reflects current behavior.
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));

    const branch1Order: Order = {
      ...mockPaidOrder,
      branchId: 456,
      branch: { id: 456 },
    };

    const branch2Order: Order = {
      ...mockPaidOrder,
      loanId: 789,
      branchId: 789,
      branch: { id: 789 },
    };

    useCase.startMonitoring();

    // Act: Emit first paid orders for multiple branches
    mockOrders.next([branch1Order, branch2Order]);
    tick(7000);
    flushMicrotasks();

    // Assert: Current implementation triggers popup for each branch (may need review per acceptance criteria)
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(2);

    // Verify storage was checked for both branches
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      1,
      456,
      jasmine.any(String)
    );
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      1,
      789,
      jasmine.any(String)
    );
  }));

  it('should reset daily tracking', () => {
    useCase.resetDailyTracking();
    // This method clears internal state, so we just verify it doesn't throw
    expect().nothing();
  });

  it('should handle manual order status check for first payment', fakeAsync(() => {
    // Setup: Manual check for first payment
    posOfflineService.checkOrderStatus.and.callFake(() => {
      return of({ content: mockPaidOrder, error: false, code: 200 });
    });

    let result: PaymentSuccessEvent | null = null;

    // Act: Check order status manually
    useCase.checkOrderStatus(123).subscribe(event => {
      result = event;
    });

    tick(7000);
    flushMicrotasks();

    // Assert: Verify side effects for first payment
    expect(result).toBeTruthy();
    expect(result!.status).toBe('Pagado');
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
  }));

  it('should handle errors gracefully', fakeAsync(() => {
    spyOn(console, 'error');
    useCase.startMonitoring();
    mockOrders.error(new Error('Test error'));
    tick(1000);
    // Verify that error was logged (side effect)
    expect(console.error).toHaveBeenCalled();
  }));
});
