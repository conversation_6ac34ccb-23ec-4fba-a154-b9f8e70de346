import {
  TestBed,
  fakeAsync,
  tick,
  flushMicrotasks,
  discardPeriodicTasks,
} from '@angular/core/testing';
import {
  CheckPaymentSuccessUseCase,
  PaymentSuccessEvent,
} from 'src/app/modules/shared/popup/application/usecases/check-payment-success.usecase';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { PosOfflineService } from 'src/app/core/services/pos-offline.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { BehaviorSubject, of } from 'rxjs';
import { Order } from 'src/app/core/domain/order.interface';
import { StoreService } from 'src/app/core/application/services/store.service';

describe('CheckPaymentSuccessUseCase', () => {
  let useCase: CheckPaymentSuccessUseCase;
  let ordersService: jasmine.SpyObj<OrdersWithSocketService>;
  let posOfflineService: jasmine.SpyObj<PosOfflineService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let storeService: jasmine.SpyObj<StoreService>;

  const mockOrders = new BehaviorSubject<Order[]>([]);

  const mockPaidOrder: Order = {
    id: 1,
    loanId: 123,
    branchId: 456,
    status: 'Pagado',
    createdAt: new Date(),
    updatedAt: new Date(),
    DeletedAt: null,
    date: new Date(),
    price: 100,
    url: 'test-url',
    merchantId: 789,
    products: [],
    branch: { id: 456 },
  };

  beforeEach(() => {
    // Reset the mock orders subject to ensure clean state
    mockOrders.next([]);

    ordersService = jasmine.createSpyObj('OrdersWithSocketService', [], {
      orders$: mockOrders,
    });

    posOfflineService = jasmine.createSpyObj('PosOfflineService', [
      'checkOrderStatus',
    ]);

    showPopupUseCase = jasmine.createSpyObj('ShowPopupUseCase', ['execute']);
    showPopupUseCase.execute.and.callFake(() => of({ success: true }));

    popupStorage = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
    ]);
    popupStorage.wasPopupShownToday.and.returnValue(false);

    storeService = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$: of({ id: 456, name: 'Test Branch' }),
    });

    TestBed.configureTestingModule({
      providers: [
        CheckPaymentSuccessUseCase,
        { provide: OrdersWithSocketService, useValue: ordersService },
        { provide: PosOfflineService, useValue: posOfflineService },
        { provide: ShowPopupUseCase, useValue: showPopupUseCase },
        { provide: PopupStorageService, useValue: popupStorage },
        { provide: StoreService, useValue: storeService },
      ],
    });

    useCase = TestBed.inject(CheckPaymentSuccessUseCase);
    // Ensure clean state for each test
    useCase.resetDailyTracking();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should start monitoring for successful payments', () => {
    useCase.startMonitoring();
    expect(ordersService.orders$).toBeDefined();
  });

  it('should trigger popup after detecting first paid order of the day', fakeAsync(() => {
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));
    useCase.startMonitoring();

    // Emit a paid order (this will be processed)
    mockOrders.next([mockPaidOrder]);

    // Wait for the 7-second timer and flush all async operations
    tick(7000);
    flushMicrotasks();
    tick(); // Additional tick for any remaining async operations

    // Verify that showPopupUseCase.execute was called (this is the main behavior)
    expect(showPopupUseCase.execute).toHaveBeenCalled();

    // Verify that the popup was triggered by checking the tracking state
    const trackingState = useCase.getTrackingState();
    expect(trackingState.get(456)).toBe(true);
  }));

  it('should not trigger popup for non-paid orders', fakeAsync(() => {
    const nonPaidOrder: Order = {
      ...mockPaidOrder,
      status: 'Pendiente',
    };

    useCase.startMonitoring();
    // Emit non-paid order (this will be processed)
    mockOrders.next([nonPaidOrder]);

    tick(7000);

    // Verify that no popup was triggered by checking tracking state
    const trackingState = useCase.getTrackingState();
    expect(trackingState.size).toBe(0); // No branches tracked
  }));

  it('should not trigger popup if already shown today', fakeAsync(() => {
    popupStorage.wasPopupShownToday.and.returnValue(true);

    useCase.startMonitoring();
    // Emit paid order (this will be processed)
    mockOrders.next([mockPaidOrder]);

    tick(7000);

    // Verify that no popup was triggered by checking tracking state
    const trackingState = useCase.getTrackingState();
    expect(trackingState.size).toBe(0); // No branches tracked
  }));

  it('should only trigger once per branch per day', fakeAsync(() => {
    // Mock wasPopupShownToday to always return false (no popup shown today)
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));

    useCase.startMonitoring();

    // First paid order
    mockOrders.next([mockPaidOrder]);
    tick(7000);
    flushMicrotasks();
    tick(); // Additional tick for any remaining async operations

    // Verify that popup was triggered for the first order
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
    let trackingState = useCase.getTrackingState();
    expect(trackingState.get(456)).toBe(true);

    // Second paid order (should not trigger again because popupTriggeredToday is set)
    const secondPaidOrder: Order = {
      ...mockPaidOrder,
      loanId: 789,
    };
    mockOrders.next([mockPaidOrder, secondPaidOrder]);
    tick(7000);
    flushMicrotasks();
    tick(); // Additional tick for any remaining async operations

    // Verify that popup was not triggered again (should still be called only once)
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);
    trackingState = useCase.getTrackingState();
    expect(trackingState.get(456)).toBe(true);
    expect(trackingState.size).toBe(1); // Only one branch tracked
  }));

  it('should handle multiple branches correctly', fakeAsync(() => {
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(of({ success: true }));
    const branch1Order: Order = {
      ...mockPaidOrder,
      branchId: 456,
      branch: { id: 456 },
    };

    const branch2Order: Order = {
      ...mockPaidOrder,
      loanId: 789,
      branchId: 789,
      branch: { id: 789 },
    };

    useCase.startMonitoring();
    // Emit orders for multiple branches (this will be processed)
    mockOrders.next([branch1Order, branch2Order]);

    tick(7000);
    flushMicrotasks();
    tick(); // Additional tick for any remaining async operations

    // Verify that popups were triggered for both branches
    // Since only one popup is triggered per timer event, we expect 1 call
    expect(showPopupUseCase.execute).toHaveBeenCalledTimes(1);

    // Verify tracking state for both branches
    const trackingState = useCase.getTrackingState();
    expect(trackingState.get(456)).toBe(true);
    expect(trackingState.get(789)).toBe(true);
  }));

  it('should reset daily tracking', () => {
    useCase.resetDailyTracking();
    // This method clears internal state, so we just verify it doesn't throw
    expect().nothing();
  });

  it('should handle manual order status check', fakeAsync(() => {
    posOfflineService.checkOrderStatus.and.callFake(() => {
      return of({ content: mockPaidOrder, error: false, code: 200 });
    });

    let result: PaymentSuccessEvent | null = null;
    useCase.checkOrderStatus(123).subscribe(event => {
      result = event;
    });

    tick(7000);
    flushMicrotasks();
    tick(); // Additional tick for any remaining async operations

    expect(result).toBeTruthy();
    expect(result!.status).toBe('Pagado');
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should handle errors gracefully', fakeAsync(() => {
    spyOn(console, 'error');
    useCase.startMonitoring();
    mockOrders.error(new Error('Test error'));
    tick(1000);
    // Verify that error was logged (side effect)
    expect(console.error).toHaveBeenCalled();
  }));
});
