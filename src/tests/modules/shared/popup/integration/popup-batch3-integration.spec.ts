import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import { PopupTriggerService } from 'src/app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { PopupEntity } from 'src/app/modules/shared/popup/domain/entities/popup.entity';
import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

/**
 * Test de integración para Batch 3 - Verifica que todos los servicios funcionan juntos
 * sin hacer llamadas HTTP reales
 */
describe('Popup Batch 3 Integration', () => {
  let popupTriggerService: PopupTriggerService;
  let popupRepository: jasmine.SpyObj<PopupRepository>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;

  const mockPopups: PopupEntity[] = [
    new PopupEntity(
      new PopupId(1),
      'Problemas con el ticket',
      '¿Tienes problemas para generar el ticket?',
      1,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
    new PopupEntity(
      new PopupId(2),
      'Sin señal',
      '¿Sin señal?',
      2,
      true,
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-06-23T00:00:00.000Z'),
      new Date('2025-07-23T00:00:00.000Z')
    ),
  ];

  beforeEach(() => {
    const popupRepositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
      'getPopupHtml',
    ]);
    const popupStorageSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'cleanupOldRecords',
      'isDontShowAgain',
    ]);
    const showPopupUseCaseSpy = jasmine.createSpyObj('ShowPopupUseCase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      providers: [
        PopupTriggerService,
        { provide: PopupRepository, useValue: popupRepositorySpy },
        { provide: PopupStorageService, useValue: popupStorageSpy },
        { provide: ShowPopupUseCase, useValue: showPopupUseCaseSpy },
      ],
    });

    popupTriggerService = TestBed.inject(PopupTriggerService);
    popupRepository = TestBed.inject(
      PopupRepository
    ) as jasmine.SpyObj<PopupRepository>;
    popupStorage = TestBed.inject(
      PopupStorageService
    ) as jasmine.SpyObj<PopupStorageService>;
    showPopupUseCase = TestBed.inject(
      ShowPopupUseCase
    ) as jasmine.SpyObj<ShowPopupUseCase>;

    // Setup default return values
    popupStorage.isDontShowAgain.and.returnValue(false);
  });

  it('should integrate all Batch 3 services without making real HTTP calls', fakeAsync(() => {
    // Mock repository to return popups without HTTP call
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: true, popupShown: mockPopups[0] })
    );

    let result = false;
    popupTriggerService.triggerAfterLogin(1).subscribe(value => {
      result = value;
    });
    tick();

    // Verify integration works
    expect(result).toBe(true);
    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(1);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalled();
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should handle no popups available scenario', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    popupTriggerService.triggerAfterPaymentSuccess(1).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should handle all popups already shown scenario', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(true);

    let result = false;
    popupTriggerService.shouldShowPopup({ branchId: 1 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
  }));

  it('should prioritize popups correctly', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.callFake((popupId: number) => {
      return popupId === 1; // POPUP-001 ya mostrado, POPUP-002 no
    });

    let result = false;
    popupTriggerService.shouldShowPopup({ branchId: 1 }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    // Should check POPUP-002 first (higher priority)
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      2,
      1,
      jasmine.any(String)
    );
  }));
});
