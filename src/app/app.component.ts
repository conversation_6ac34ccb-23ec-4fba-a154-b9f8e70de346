import { AsyncPipe } from '@angular/common';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import {
  LoaderService,
  MerchantChatAttributes,
  WebchatService,
} from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import {
  Subject,
  delayWhen,
  filter,
  map,
  switchMap,
  takeUntil,
  withLatestFrom,
  take,
} from 'rxjs';
import packageJson from '../../package.json';
import { StoreService } from './core/application/services/store.service';
import { MerchantsService } from './core/services/merchants.service';
import { PopupTriggerService } from './modules/shared/popup/application/services/popup-trigger.service';
import { CheckPaymentSuccessUseCase } from './modules/shared/popup/application/usecases/check-payment-success.usecase';

@Component({
  standalone: true,
  selector: 'app-root',
  template: `
    <router-outlet></router-outlet>

    <aplz-ui-pill-loader
      [loading]="(isLoading$ | async) === true"
      [messages]="(loaderMessage$ | async)!"></aplz-ui-pill-loader>
  `,
  imports: [RouterOutlet, AplazoPillLoaderComponent, AsyncPipe],
})
export class AppComponent implements OnInit, OnDestroy {
  readonly #router: Router = inject(Router);
  readonly #storeService: StoreService = inject(StoreService);
  readonly #loaderService: LoaderService = inject(LoaderService);
  readonly #tagService = inject(TagManagerService);
  readonly #webchatService: WebchatService<MerchantChatAttributes> =
    inject(WebchatService);
  readonly #version = packageJson.version;
  readonly #merchantService = inject(MerchantsService);
  readonly #popupTriggerService = inject(PopupTriggerService);
  readonly #checkPaymentSuccessUseCase = inject(CheckPaymentSuccessUseCase);

  readonly isLoading$ = this.#loaderService.isLoading$;
  readonly loaderMessage$ = this.#loaderService.loaderMessage$;

  readonly #destroy$: Subject<void> = new Subject<void>();

  ngOnInit() {
    this.#popupTriggerService.cleanupOldRecords();

    // For testing: Clear today's popup records to ensure popups can be shown
    const today = new Date().toISOString().split('T')[0];
    const popupStorage = this.#popupTriggerService['popupStorage'];
    if (
      popupStorage &&
      typeof popupStorage.clearRecordsForDate === 'function'
    ) {
      popupStorage.clearRecordsForDate(today);
    }

    // Start monitoring for successful payments to trigger popups
    this.#checkPaymentSuccessUseCase.startMonitoring();

    // Removed popup trigger initialization - popups will only be triggered after first successful payment of the day

    this.#router.events
      .pipe(
        filter(e => e instanceof NavigationEnd),
        withLatestFrom(
          this.#storeService.merchantName$,
          this.#storeService.merchantConfig$,
          this.#storeService.selectedBranch$
        ),
        takeUntil(this.#destroy$)
      )
      .subscribe(([, merchantName, merchantConfig, selectedBranch]) => {
        this.#tagService.trackEvent({
          event: 'pageView',
          merchantId: merchantConfig?.merchantId || 0,
          merchantName: merchantName || '',
          branchId: selectedBranch?.id || 0,
          branchName: selectedBranch?.name || '',
        });
      });

    this.#router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.#destroy$)
      )
      .subscribe(() => {
        const isMainViews =
          document.location.pathname.includes('cart') ||
          document.location.pathname.includes('orders');

        if (!isMainViews) {
          this.#webchatService.hide();
          return;
        }

        this.#webchatService.show();
      });

    this.#storeService.token$
      .pipe(
        filter(Boolean),
        delayWhen(() =>
          this.#webchatService.chatInitialized$.pipe(filter(Boolean))
        ),
        switchMap(() => this.#merchantService.getMerchantDetails()),
        withLatestFrom(
          this.#storeService.selectedBranch$,
          this.#storeService.getMerchantId$(),
          this.#storeService.merchantName$,
          this.#storeService.integrationType$
        ),
        map(
          ([
            merchantDetails,
            branch,
            merchantId,
            merchantName,
            integrationType,
          ]) => {
            const attributes = {
              sharedEmails: merchantDetails.email
                ? [merchantDetails.email]
                : [],
              sharedPhones: [],
            };

            const customAttributes = {
              merchantIdNum: merchantId || 0,
              merchantNameStr: merchantName || '',
              merchantintegrationStr: integrationType || '',
              storefrontNameStr: branch?.name || '',
              storefrontIdNum: branch?.id || 0,
              correoStr: merchantDetails.email || '',
              statusStr: merchantDetails.status || '',
              merchantchannelStr: merchantDetails.channel || '',
              merchantsegmentStr: merchantDetails.segment || '',
            };

            return {
              attributes,
              customAttributes,
            };
          }
        ),
        takeUntil(this.#destroy$)
      )
      .subscribe(({ attributes, customAttributes }) => {
        this.#webchatService.setCustomerChatAttributes({
          attributes,
          customAttributes,
        });

        // Removed popup trigger after login to prevent showing popup at app startup
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
