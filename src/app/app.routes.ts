import { inject } from '@angular/core';
import { Router, Routes } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { ROUTE_CONFIG } from './core/domain/config/app-routes.core';
import { allowByIntegrationGuard } from './core/guards/allow-by-integration.guard';
import { allowNavigationWhenOnline } from './core/guards/disallow-navigation-when-offline.guard';
import { HasBranchCreationEnabledGuard } from './core/guards/has-branch-creation-enabled.guard';
import { HasBranchOfficesGuard } from './core/guards/has-branch-offices.guard';
import { HasBranchSelectedGuard } from './core/guards/has-branch-selected.guard';
import { hasNotSelectedBranchGuard } from './core/guards/has-empty-branch-selected.guard';
import { hasValidRoleGuard } from './core/guards/has-valid-role.guard';
import { isUnderMaintenanceGuard } from './core/guards/maintenance.guard';
import { NoLoggedOnlyGuard } from './core/guards/no-logged-only.guard';
import { OnlyLoggedInGuard } from './core/guards/only-logged-in.guard';
import { CartV1Component } from './modules/cart-v1/cart-v1.component';
import {
  provideRefundsRepositories,
  provideRefundsUseCases,
} from './modules/orders-v1/config/provider';
import { OrdersV1Component } from './modules/orders-v1/orders-v1.component';
import { OrdersV2Component } from './modules/orders-v2/infra/pages/orders-v2.component';
import { SecuredV1Component } from './modules/secured-v1/secured-v1.component';
import { SelectBranchComponent } from './modules/select-branch/select-branch.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: ROUTE_CONFIG.authentication,
    pathMatch: 'full',
  },
  {
    path: ROUTE_CONFIG.authentication,
    canActivate: [isUnderMaintenanceGuard, NoLoggedOnlyGuard],
    loadChildren: () => import('./modules/newAuth/auth.routes'),
  },

  {
    path: ROUTE_CONFIG.securedSelectBranch,
    canActivate: [
      isUnderMaintenanceGuard,
      allowNavigationWhenOnline,
      OnlyLoggedInGuard,
      hasValidRoleGuard,
      HasBranchOfficesGuard,
      hasNotSelectedBranchGuard,
    ],
    canDeactivate: [allowNavigationWhenOnline],
    component: SelectBranchComponent,
  },

  {
    path: ROUTE_CONFIG.securedNewBranch,
    canActivate: [
      isUnderMaintenanceGuard,
      allowNavigationWhenOnline,
      OnlyLoggedInGuard,
      hasValidRoleGuard,
      HasBranchCreationEnabledGuard,
    ],
    canLoad: [allowNavigationWhenOnline],
    loadChildren: () => import('./modules/new-branch/new-branch.routes'),
  },

  {
    path: ROUTE_CONFIG.aplazoRoot,
    canActivate: [
      isUnderMaintenanceGuard,
      allowNavigationWhenOnline,
      OnlyLoggedInGuard,
      hasValidRoleGuard,
      allowByIntegrationGuard(),
      HasBranchOfficesGuard,
      HasBranchSelectedGuard,
    ],
    canLoad: [allowNavigationWhenOnline],
    children: [
      {
        path: '',
        redirectTo: ROUTE_CONFIG.aplazoLayout,
        pathMatch: 'full',
      },

      {
        path: ROUTE_CONFIG.aplazoLayout,
        component: SecuredV1Component,
        canDeactivate: [allowNavigationWhenOnline],
        canActivate: [allowNavigationWhenOnline],
        children: [
          {
            path: '',
            redirectTo: ROUTE_CONFIG.securedOrdersV2,
            pathMatch: 'full',
          },
          {
            path: ROUTE_CONFIG.securedOrdersV2,
            data: { i18nTitle: 'ordersV2' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [
              allowNavigationWhenOnline,
              () => {
                const router = inject(Router);
                const flag = inject(StatsigService).checkGate(
                  'b2b_front_posui_sse_enabled'
                );

                if (!flag) {
                  return router.parseUrl(
                    `/${ROUTE_CONFIG.aplazoRoot}/${ROUTE_CONFIG.aplazoLayout}/${ROUTE_CONFIG.securedOrders}`
                  );
                }

                return true;
              },
            ],
            canLoad: [allowNavigationWhenOnline],
            providers: [provideRefundsRepositories(), provideRefundsUseCases()],
            component: OrdersV2Component,
          },
          {
            path: ROUTE_CONFIG.securedOrders,
            data: { i18nTitle: 'orders' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [
              allowNavigationWhenOnline,
              () => {
                const router = inject(Router);
                const flag = inject(StatsigService).checkGate(
                  'b2b_front_posui_sse_enabled'
                );

                if (flag) {
                  return router.parseUrl(
                    `/${ROUTE_CONFIG.aplazoRoot}/${ROUTE_CONFIG.aplazoLayout}/${ROUTE_CONFIG.securedOrdersV2}`
                  );
                }

                return true;
              },
            ],
            canLoad: [allowNavigationWhenOnline],
            providers: [provideRefundsRepositories(), provideRefundsUseCases()],
            component: OrdersV1Component,
          },
          {
            path: ROUTE_CONFIG.securedHistoric,
            data: { i18nTitle: 'historical' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [allowNavigationWhenOnline],
            canLoad: [allowNavigationWhenOnline],
            loadComponent: () =>
              import('./modules/historical-v1/historical-v1.component').then(
                m => m.HistoricalV1Component
              ),
          },
          {
            path: ROUTE_CONFIG.securedCart,
            data: { i18nTitle: 'cart' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [allowNavigationWhenOnline],
            canLoad: [allowNavigationWhenOnline],
            component: CartV1Component,
          },
          {
            path: ROUTE_CONFIG.preloanCart,
            data: { i18nTitle: 'preloan' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [allowNavigationWhenOnline],
            component: CartV1Component,
          },
          {
            path: ROUTE_CONFIG.aplazoNotifications,
            data: { i18nTitle: 'notifications' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [allowNavigationWhenOnline],
            canLoad: [allowNavigationWhenOnline],
            loadComponent: () =>
              import(
                './modules/notifications-v1/notifications-v1.component'
              ).then(m => m.NotificationsV1Component),
          },
          {
            path: ROUTE_CONFIG.aplazoContentMedia,
            data: { i18nTitle: 'aplazoVersity' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [
              allowNavigationWhenOnline,
              () => {
                const router = inject(Router);
                const flag = inject(StatsigService).checkGate(
                  'b2b_front_posui_aplazoversity'
                );

                if (!flag) {
                  return router.parseUrl(ROUTE_CONFIG.securedOrders);
                }

                return true;
              },
            ],
            canLoad: [allowNavigationWhenOnline],
            loadComponent: () =>
              import('./modules/media-content/media-content.component').then(
                m => m.MediaContentComponent
              ),
          },
          {
            path: ROUTE_CONFIG.challenges,
            data: { i18nTitle: 'challenges' },
            canActivate: [OnlyLoggedInGuard],
            loadComponent: () =>
              import('./modules/challenges/challenges-page.component').then(
                m => m.ChallengesPageComponent
              ),
          },
          {
            path: ROUTE_CONFIG.contestEntries,
            data: { i18nTitle: 'premios' },
            canActivate: [OnlyLoggedInGuard],
            loadChildren: () =>
              import(
                './modules/contest-entries/infra/routes/contest-entries.routes'
              ),
          },
          {
            path: ROUTE_CONFIG.storeSupport,
            data: { i18nTitle: 'storeSupport' },
            canDeactivate: [allowNavigationWhenOnline],
            canActivate: [
              allowNavigationWhenOnline,
              () => {
                const router = inject(Router);
                const flag = inject(StatsigService).checkGate(
                  'b2b_front_posui_ayuda_tiendas_section'
                );

                if (!flag) {
                  return router.parseUrl(ROUTE_CONFIG.securedOrders);
                }

                return true;
              },
            ],
            canLoad: [allowNavigationWhenOnline],
            loadChildren: () =>
              import(
                './modules/store-support/infra/routes/store-support.routes'
              ),
          },
        ],
      },
    ],
  },
  {
    path: ROUTE_CONFIG.successView,
    canActivate: [isUnderMaintenanceGuard],
    loadComponent: () =>
      import('./modules/success-page/success-page.component').then(
        m => m.SuccessPageComponent
      ),
  },
  {
    path: ROUTE_CONFIG.wrongIntegrationView,
    canActivate: [isUnderMaintenanceGuard, OnlyLoggedInGuard],
    loadComponent: () =>
      import('./modules/wrong-integration-page/error-page.component').then(
        m => m.WrongIntegrationPageComponent
      ),
  },
  {
    path: ROUTE_CONFIG.unavailable,
    canActivate: [isUnderMaintenanceGuard],
    loadComponent: () =>
      import('./modules/maintenance/maintenance-message.component').then(
        m => m.MaintenanceMessageComponent
      ),
  },
  {
    path: '**',
    redirectTo: ROUTE_CONFIG.authentication,
    pathMatch: 'full',
  },
];
