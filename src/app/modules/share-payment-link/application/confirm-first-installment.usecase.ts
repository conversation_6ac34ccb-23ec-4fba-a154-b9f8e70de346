import { Injectable } from '@angular/core';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { DialogRef } from '@ngneat/dialog';
import {
  EMPTY,
  MonoTypeOperatorFunction,
  catchError,
  finalize,
  pipe,
  take,
  tap,
} from 'rxjs';
import { PaymentsRepository } from '../../../core/domain/repositories/payments.repository';
import { PopupTriggerService } from '../../shared/popup/application/services/popup-trigger.service';

@Injectable({ providedIn: 'root' })
export class ConfirmFirstInstallmentUsecase {
  constructor(
    private paymentsService: PaymentsRepository,
    private loader: LoaderService,
    private notifier: NotifierService,
    private popupTrigger: PopupTriggerService
  ) {}

  public execute(
    loanId: number,
    phone: number,
    authCode: string,
    schemeId: number,
    dialogRef?: DialogRef<any, any>
  ): void {
    const loaderId = this.loader.show();
    const phoneWithInternationalCode = `52${phone}`;

    if (phoneWithInternationalCode.length !== 12) {
      this.loader.hide(loaderId);
      this.notifier.warning({
        title: 'Número de teléfono inválido',
        message: 'El número de teléfono debe tener 10 dígitos',
      });
      return;
    }

    if (!schemeId) {
      this.loader.hide(loaderId);
      this.notifier.warning({
        title: 'Error al procesar plan de pagos',
        message: 'El esquema de pagos no es válido',
      });
      return;
    }

    this.paymentsService
      .confirmFirstInstallment(
        loanId,
        phoneWithInternationalCode,
        authCode.toUpperCase(),
        schemeId
      )
      .pipe(this.#showSuccessOrError(loaderId, dialogRef))
      .subscribe();
  }

  #showSuccessOrError(
    loaderId: string,
    dialogRef?: DialogRef
  ): MonoTypeOperatorFunction<any> {
    return pipe(
      take(1),

      tap(() => {
        this.notifier.success({
          title: 'Compra confirmada, actualice listado de ordenes',
        });
        this.popupTrigger.triggerAfterPaymentSuccess(0).subscribe();
      }),

      catchError((error: Error) => {
        this.notifier.warning({
          title: '¡Ups! Algo salió mal',
          message: 'No se pudo confirmar la compra: ' + error.message,
        });

        return EMPTY;
      }),

      finalize(() => {
        this.loader.hide(loaderId);
      })
    );
  }
}
