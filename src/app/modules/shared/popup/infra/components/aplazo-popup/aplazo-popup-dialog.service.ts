import { Injectable, inject } from '@angular/core';
import { DialogService } from '@ngneat/dialog';
import { AplazoPopupComponent } from './aplazo-popup.component';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AplazoPopupDialogService {
  private dialog = inject(DialogService);

  /**
   * Opens the dialog with the popup HTML content and popup ID.
   * @param htmlContent - HTML content of the popup
   * @param popupId - ID of the popup
   * @returns Observable that emits when the dialog closes
   */
  openPopup(htmlContent: string, popupId: number): Observable<any> {
    if (!this.dialog) {
      return of(null); // Return fallback if DialogService is not available
    }

    let dialogRef;
    try {
      dialogRef = this.dialog.open(AplazoPopupComponent, {
        data: { htmlContent, popupId },
        maxWidth: '600px',
        enableClose: true,
      });
    } catch (error) {
      return of(null); // Return fallback if opening dialog fails
    }

    // Check if dialogRef and afterClosed$ are defined to prevent errors
    if (dialogRef && dialogRef.afterClosed$) {
      return dialogRef.afterClosed$;
    } else {
      return of(null); // Return a fallback Observable to prevent errors
    }
  }
}
