import { Component, inject, signal } from '@angular/core';
import { DialogRef } from '@ngneat/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { PopupStorageService } from '../../../application/services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';

@Component({
  selector: 'aplazo-popup',
  standalone: true,
  template: `
    <div
      class="w-full h-full flex items-center justify-center bg-black bg-opacity-60">
      <div
        class="bg-white rounded-lg shadow-lg p-8 min-w-[320px] max-w-xl relative">
        <button
          type="button"
          class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
          aria-label="Cerrar"
          (click)="close()">
          &times;
        </button>
        <div [innerHTML]="safeHtml()"></div>

        <!-- "Don't show again" checkbox -->
        <div class="mt-4 pt-4 border-t border-gray-200">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              [checked]="dontShowAgain()"
              (change)="onDontShowAgainChange($event)"
              class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
            <span class="text-sm text-gray-700">No mostrar de nuevo</span>
          </label>
        </div>
      </div>
    </div>
  `,
})
export class AplazoPopupComponent {
  private dialogRef = inject(DialogRef);
  private sanitizer = inject(DomSanitizer);
  private popupStorageService = inject(PopupStorageService);
  private storeService = inject(StoreService);

  safeHtml = signal<SafeHtml>(
    this.sanitizer.bypassSecurityTrustHtml(
      this.dialogRef.data?.htmlContent || '<p>No content</p>'
    )
  );

  dontShowAgain = signal<boolean>(false);

  close() {
    // If the checkbox is checked, save the preference
    if (this.dontShowAgain()) {
      this.saveDontShowAgainPreference();
    }
    this.dialogRef.close();
  }

  onDontShowAgainChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    this.dontShowAgain.set(checkbox.checked);
  }

  private saveDontShowAgainPreference() {
    // Get the popupId from the dialog (assuming it's passed in the data)
    const popupId = Number(this.dialogRef.data?.popupId);

    if (popupId) {
      // Get the current branchId
      this.storeService.selectedBranch$.subscribe(branch => {
        if (branch?.id) {
          const branchId = Number(branch.id);
          this.popupStorageService.saveDontShowAgain(popupId, branchId);
        }
      });
    }
  }
}
