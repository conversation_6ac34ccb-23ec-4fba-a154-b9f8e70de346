import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError, map, tap } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../../../app-core/domain/environments';
import { PopupEntity } from '../../domain/entities/popup.entity';
import { PopupId } from '../../domain/value-objects/popup-id.value-object';

// API Response interface matching backend structure (LEGACY - for backward compatibility)
export interface PopupApiResponse {
  popupId: number;
  title: string;
  content: string;
  priority: number;
  startAt?: string;
  endAt?: string;
  createdAt: string;
  updatedAt: string;
}

// NEW: Real API Response interface for metadata endpoint
export interface PopupMetaResponse {
  popupId: number;
  title: string;
  priority: number;
  startAt?: string;
  endAt?: string;
}

export interface PopupMeta {
  popupId: number;
  startAt: string;
  endAt: string;
  priority: number;
}

@Injectable({ providedIn: 'root' })
export class PopupRepository {
  private http = inject(HttpClient);
  private environment = inject<PosEnvironmentCoreType>(POS_ENVIRONMENT_CORE);
  private apiUrl = this.environment.promoApiUrl.replace(/\/+$/, '');

  /**
   * Maps API response to PopupEntity instances (LEGACY)
   * @param apiResponses - Array of API response objects
   * @returns Array of PopupEntity instances
   */
  private mapApiResponseToEntities(
    apiResponses: PopupApiResponse[]
  ): PopupEntity[] {
    return apiResponses
      .map(response => this.mapSingleApiResponseToEntity(response))
      .filter(entity => entity !== null) as PopupEntity[];
  }

  /**
   * Maps metadata responses from real API to PopupEntity instances
   * @param metaResponses - Array of metadata response objects
   * @returns Array of PopupEntity instances
   */
  private mapMetaResponseToEntities(
    metaResponses: PopupMetaResponse[]
  ): PopupEntity[] {
    return metaResponses
      .map(response => this.mapMetaToEntity(response))
      .filter(entity => entity !== null) as PopupEntity[];
  }

  /**
   * Maps a single API response to PopupEntity (LEGACY)
   * @param response - Single API response object
   * @returns PopupEntity instance or null if mapping fails
   */
  private mapSingleApiResponseToEntity(
    response: PopupApiResponse
  ): PopupEntity | null {
    try {
      const popupId = PopupId.fromNumber(Number(response.popupId));
      const startAt = response.startAt ? new Date(response.startAt) : undefined;
      const endAt = response.endAt ? new Date(response.endAt) : undefined;
      const createdAt = new Date(response.createdAt);
      const now = new Date();

      // Calculate isActive based on current date and date ranges
      const isActive =
        startAt && endAt
          ? now >= startAt && now <= endAt
          : startAt
            ? now >= startAt
            : endAt
              ? now <= endAt
              : true; // If no dates are set, consider it active

      return new PopupEntity(
        popupId,
        response.title,
        response.content,
        response.priority,
        isActive,
        createdAt,
        startAt,
        endAt
      );
    } catch (error) {
      console.error(
        'Error mapping API response to PopupEntity:',
        error,
        response
      );
      return null;
    }
  }

  /**
   * Maps metadata response from real API to PopupEntity
   * @param metaResponse - Metadata from real API
   * @returns PopupEntity instance or null if mapping fails
   */
  private mapMetaToEntity(metaResponse: PopupMetaResponse): PopupEntity | null {
    try {
      const popupId = PopupId.fromNumber(Number(metaResponse.popupId));
      const startAt = metaResponse.startAt
        ? new Date(metaResponse.startAt)
        : undefined;
      const endAt = metaResponse.endAt
        ? new Date(metaResponse.endAt)
        : undefined;

      // Default values for fields not provided by real API
      const defaultTitle = `Popup ${metaResponse.popupId}`;
      const defaultContent = ''; // Will be loaded lazily
      const defaultIsActive = true; // Assume active if returned by API
      const defaultCreatedAt = new Date(); // Use current date as fallback

      return new PopupEntity(
        popupId,
        defaultTitle,
        defaultContent,
        metaResponse.priority,
        defaultIsActive,
        defaultCreatedAt,
        startAt,
        endAt
      );
    } catch (error) {
      console.error(
        'Error mapping metadata response to PopupEntity:',
        error,
        metaResponse
      );
      return null;
    }
  }

  /**
   * GET /api/v1/popups?branchId={branchId}
   * Gets available popups for a specific branch
   * @param branchId - The branch ID to fetch popups for
   * @returns Observable that emits an array of PopupEntity instances
   */
  getAvailablePopups(branchId: number): Observable<PopupEntity[]> {
    // Real API implementation
    return this.http
      .get<PopupMetaResponse[]>(
        `${this.apiUrl}/api/v1/popups?branchId=${branchId}`
      )
      .pipe(
        map(
          metaResponses =>
            metaResponses
              .map(meta => this.mapMetaToEntity(meta))
              .filter(Boolean) as PopupEntity[]
        ),
        catchError((error: unknown) => {
          console.warn(
            '⚠️ [PopupRepository] Error fetching popups from real API, using mock data:',
            error
          );
          const mockPopups = this.getMockPopups();
          return of(this.mapApiResponseToEntities(mockPopups));
        })
      );
  }

  /**
   * GET /api/v1/popups/content/{popupId}
   * Gets the HTML content of a specific popup
   */
  getPopupHtml(popupId: number = 1): Observable<string> {
    return this.http
      .get(`${this.apiUrl}/api/v1/popups/content/${popupId}`, {
        responseType: 'text', // Important: expect HTML string, not JSON
      })
      .pipe(
        tap(() => {}),
        catchError((error: unknown) => {
          console.warn(
            '⚠️ [PopupRepository] Error fetching popup HTML from real API, using mock data:',
            error
          );
          return this.getMockPopupHtml(popupId);
        })
      );
  }

  /**
   * Mock data as fallback when the API is not available
   */
  private getMockPopups(): PopupApiResponse[] {
    return [
      {
        popupId: 1,
        title: '¡Bienvenido a Aplazo!',
        content:
          '<h2>¡Bienvenido a Aplazo!</h2><p>Gracias por usar nuestra plataforma.</p>',
        priority: 1,
        startAt: '2023-01-01T00:00:00.000Z',
        endAt: '2025-12-31T23:59:59.999Z',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      },
      {
        popupId: 2,
        title: 'Sin señal',
        content: '¿Sin señal?',
        priority: 2,
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
        createdAt: '2025-06-23T00:00:00.000Z',
        updatedAt: '2025-06-23T00:00:00.000Z',
      },
    ];
  }

  /**
   * Mock HTML as fallback when the API is not available
   */
  private getMockPopupHtml(popupId: number): Observable<string> {
    const popup = this.getMockPopups().find(p => p.popupId === popupId);

    if (!popup) {
      return of('<h2>Popup no encontrado</h2>');
    }

    // Validar fechas de vigencia
    const now = new Date();
    if (popup.startAt && new Date(popup.startAt) > now) {
      return of(''); // Retornar string vacío en lugar de null
    }
    if (popup.endAt && new Date(popup.endAt) < now) {
      return of(''); // Retornar string vacío en lugar de null
    }

    // Retornar el contenido HTML según el ID
    if (popupId === 1) {
      return of('<h2>¿Tienes problemas para generar el ticket?</h2>');
    } else if (popupId === 2) {
      return of('<h2>¿Sin señal?</h2>');
    }

    return of('<h2>Popup no encontrado</h2>');
  }
}
