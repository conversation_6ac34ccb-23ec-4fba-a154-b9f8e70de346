/**
 * PopupId value object.
 * Represents a unique identifier for popups with validation and immutability.
 */
export class PopupId {
  constructor(private readonly value: number) {
    if (!this.isValid(value)) {
      throw new Error('Invalid PopupId: ID must be a positive number');
    }
  }

  /**
   * Gets the string value of the ID.
   * @returns The ID as a string
   */
  getValue(): number {
    return this.value;
  }

  /**
   * Checks if this ID equals another ID.
   * @param other - The other PopupId to compare with
   * @returns True if the IDs are equal, false otherwise
   */
  equals(other: PopupId): boolean {
    return this.value === other.value;
  }

  /**
   * Creates a string representation of the ID.
   * @returns The ID as a string
   */
  toString(): string {
    return this.value.toString();
  }

  /**
   * Validates if the provided value is a valid popup ID.
   * @param value - The value to validate
   * @returns True if the value is valid, false otherwise
   */
  private isValid(value: number): boolean {
    return typeof value === 'number' && !isNaN(value) && value > 0;
  }

  /**
   * Creates a PopupId from a number value.
   * @param value - The number value to create the ID from
   * @returns A new PopupId instance
   */
  static fromNumber(value: number): PopupId {
    return new PopupId(value);
  }

  /**
   * Creates a random PopupId.
   * @returns A new PopupId with a random UUID
   */
  static random(): PopupId {
    const randomId = Math.floor(Math.random() * 1000000) + 1;
    return new PopupId(randomId);
  }
}
