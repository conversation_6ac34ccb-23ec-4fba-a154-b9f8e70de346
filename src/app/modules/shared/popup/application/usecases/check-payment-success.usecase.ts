import { Injectable, inject } from '@angular/core';
import { Observable, of, timer } from 'rxjs';
import { switchMap, take, tap, catchError, filter, skip } from 'rxjs/operators';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { PosOfflineService } from 'src/app/core/services/pos-offline.service';
import { Order } from 'src/app/core/domain/order.interface';
import { ShowPopupUseCase } from './show-popup.usecase';
import { PopupStorageService } from '../services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';

export interface PaymentSuccessEvent {
  loanId: number;
  branchId: number;
  status: string;
  timestamp: Date;
}

/**
 * Use case for detecting successful payments and triggering popups.
 * Monitors state changes in orders and detects when an order
 * changes to "APROBADO" status (equivalent to "Approved").
 * According to acceptance criteria: Pop-Up is triggered 7 seconds after the first successful repayment (status = "APROBADO") of the day.
 */
@Injectable({ providedIn: 'root' })
export class CheckPaymentSuccessUseCase {
  private ordersService = inject(OrdersWithSocketService);
  private posOfflineService = inject(PosOfflineService);
  private showPopupUseCase = inject(ShowPopupUseCase);
  private popupStorage = inject(PopupStorageService);
  private storeService = inject(StoreService);

  // Track if popup has been triggered today for each branch
  private popupTriggeredToday = new Map<number, boolean>();

  /**
   * Starts monitoring for successful payments.
   * Automatically executes when a status change to "APROBADO" is detected.
   */
  startMonitoring(): void {
    // Reset tracking when starting monitoring
    this.popupTriggeredToday.clear();

    this.ordersService.orders$
      .pipe(
        filter((orders: Order[]) => orders.length > 0), // Only process non-empty arrays
        switchMap((orders: Order[]) => {
          // Find orders that recently changed to "APROBADO" status
          const paidOrders = orders.filter(
            (order: Order) => order.status.toLowerCase() === 'aprobado'
          );

          if (paidOrders.length === 0) {
            return of(null);
          }

          // Check if any order recently changed to "APROBADO"
          return this.checkRecentStatusChanges(paidOrders);
        }),
        catchError(error => {
          console.error('Error monitoring payment success:', error);
          return of(null);
        })
      )
      .subscribe();
  }

  /**
   * Checks if there are orders that recently changed to "APROBADO" status.
   * Only triggers popup for the first successful payment of the day per branch.
   * @param paidOrders - List of approved orders
   * @returns Observable with the order that recently changed
   */
  private checkRecentStatusChanges(
    paidOrders: Order[]
  ): Observable<PaymentSuccessEvent | null> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    // Find the first paid order for each branch
    const firstPaidOrderByBranch = new Map<number, Order>();

    for (const order of paidOrders) {
      const branchId = order.branchId || order.branch?.id;
      if (branchId && !firstPaidOrderByBranch.has(branchId)) {
        firstPaidOrderByBranch.set(branchId, order);
      }
    }

    // Check if we have any new first payments of the day
    const newFirstPayments: Order[] = [];

    for (const [branchId, order] of firstPaidOrderByBranch) {
      // Check if popup has already been triggered today for this branch
      const hasTriggeredToday = this.popupTriggeredToday.get(branchId) || false;

      if (!hasTriggeredToday) {
        // Check if this is actually the first payment of the day
        const popupShownToday = this.popupStorage.wasPopupShownToday(
          1,
          branchId,
          today
        );

        if (!popupShownToday) {
          newFirstPayments.push(order);
        }
      }
    }

    if (newFirstPayments.length === 0) {
      return of(null);
    }

    // Use the first payment as the representative event
    const recentPaidOrder = newFirstPayments[0];
    const branchId = recentPaidOrder.branchId || recentPaidOrder.branch?.id;

    if (!branchId) {
      return of(null);
    }

    const paymentEvent: PaymentSuccessEvent = {
      loanId: recentPaidOrder.loanId,
      branchId: branchId,
      status: recentPaidOrder.status,
      timestamp: new Date(),
    };

    console.log(
      `First successful payment of the day detected for ${newFirstPayments.length} branch(es). Triggering popup in 7 seconds...`
    );

    // Trigger the popup after 7 seconds as per acceptance criteria
    return timer(7000).pipe(
      take(1),
      tap(() => {
        // Trigger popup for each branch that had its first payment
        for (const order of newFirstPayments) {
          const orderBranchId = order.branchId || order.branch?.id;
          if (orderBranchId) {
            console.log(`Triggering popup for branch ${orderBranchId}`);
            this.triggerPopupAfterPaymentSuccess(orderBranchId);
          }
        }
      }),
      switchMap(() => of(paymentEvent))
    );
  }

  /**
   * Trigger to show popup after a successful payment.
   * Executes 7 seconds after detecting the successful payment.
   * @param branchId - The branch ID where the payment was successful
   */
  private triggerPopupAfterPaymentSuccess(branchId: number): void {
    console.log(
      `Triggering popup for branch ${branchId} after successful payment`
    );

    // Mark as triggered immediately to prevent duplicate triggers
    this.popupTriggeredToday.set(branchId, true);

    this.showPopupUseCase.execute().subscribe({
      next: result => {
        if (result.success) {
          console.log(`Popup shown successfully for branch ${branchId}`);
        } else {
          console.log(
            `No popup shown for branch ${branchId}: ${result.reason}`
          );
        }
      },
      error: error => {
        console.error(`Error showing popup for branch ${branchId}:`, error);
      },
    });
  }

  /**
   * Manually checks the status of a specific order.
   * @param loanId - ID of the order to check
   * @returns Observable with the verification result
   */
  checkOrderStatus(loanId: number): Observable<PaymentSuccessEvent | null> {
    return this.posOfflineService.checkOrderStatus(loanId).pipe(
      switchMap(response => {
        if (!response.content) {
          return of(null);
        }

        const order = response.content;
        const branchId = order.branchId || order.branch?.id;

        if (order.status.toLowerCase() === 'aprobado' && branchId) {
          const paymentEvent: PaymentSuccessEvent = {
            loanId: order.loanId,
            branchId: branchId,
            status: order.status,
            timestamp: new Date(),
          };

          // Trigger the popup after 7 seconds
          return timer(7000).pipe(
            take(1),
            tap(() => {
              this.triggerPopupAfterPaymentSuccess(branchId);
            }),
            switchMap(() => of(paymentEvent))
          );
        }

        return of(null);
      }),
      catchError(error => {
        console.error('Error checking order status:', error);
        return of(null);
      })
    );
  }

  /**
   * Resets the daily trigger tracking. Should be called at the start of each day.
   */
  resetDailyTracking(): void {
    this.popupTriggeredToday.clear();
  }

  /**
   * Gets the current tracking state for testing purposes.
   * @returns Map of branch IDs to whether popup has been triggered today
   */
  getTrackingState(): Map<number, boolean> {
    return new Map(this.popupTriggeredToday);
  }
}
