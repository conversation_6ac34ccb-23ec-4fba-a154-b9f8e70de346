import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { switchMap, take, map } from 'rxjs/operators';
import { PopupRepository } from '../../infra/repositories/popup.repository';
import { PopupEntity } from '../../domain/entities/popup.entity';
import { AplazoPopupDialogService } from '../../infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { PopupStorageService } from '../services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';

export interface ShowPopupResult {
  success: boolean;
  popupShown?: PopupEntity;
  reason?: string;
}

/**
 * Use case for displaying popups with complete business logic.
 * Handles popup selection, validations, and display state management.
 */
@Injectable({ providedIn: 'root' })
export class ShowPopupUseCase {
  private popupRepo = inject(PopupRepository);
  private dialogService = inject(AplazoPopupDialogService);
  private storageService = inject(PopupStorageService);
  private storeService = inject(StoreService);

  /**
   * Executes the business logic to display a popup.
   * @returns Observable with the operation result
   */
  execute(): Observable<ShowPopupResult> {
    return this.storeService.selectedBranch$.pipe(
      take(1),
      switchMap(branch => {
        const branchId = branch?.id ? branch.id : undefined;

        if (!branchId) {
          return of({ success: false, reason: 'No branch selected' });
        }

        return this.popupRepo.getAvailablePopups(branchId).pipe(
          switchMap(popups => {
            if (!popups || popups.length === 0) {
              return of({ success: false, reason: 'No popups available' });
            }

            // Filter popups already shown today - use the same format as PopupTriggerService
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

            const unshownPopups = popups.filter(
              popup =>
                !this.storageService.wasPopupShownToday(
                  popup.popupId,
                  branchId,
                  today
                ) &&
                !this.storageService.isDontShowAgain(popup.popupId, branchId) &&
                popup.canBeDisplayed()
            );

            if (unshownPopups.length === 0) {
              return of({
                success: false,
                reason:
                  'All popups already shown today or marked as dont show again',
              });
            }

            // Select the highest priority popup
            const selectedPopup = unshownPopups.reduce((prev, curr) =>
              curr.priority > prev.priority ? curr : prev
            );

            // Get the HTML content for the selected popup
            // Convert popupId to number for repository method
            return this.popupRepo
              .getPopupHtml(Number(selectedPopup.popupId))
              .pipe(
                map(htmlContent => {
                  // Mark as shown
                  this.storageService.markPopupAsShown(
                    selectedPopup.popupId,
                    branchId,
                    today
                  );

                  // Display the popup
                  this.dialogService.openPopup(
                    htmlContent,
                    Number(selectedPopup.popupId)
                  );

                  return {
                    success: true,
                    popupShown: selectedPopup,
                  };
                })
              );
          })
        );
      })
    );
  }

  /**
   * Checks if there are available popups to display.
   * @returns Observable with true if there are available popups
   */
  hasAvailablePopups(): Observable<boolean> {
    return this.storeService.selectedBranch$.pipe(
      take(1),
      switchMap(branch => {
        const branchId = branch?.id ? branch.id : undefined;

        if (!branchId) {
          return of(false);
        }

        return this.popupRepo.getAvailablePopups(branchId).pipe(
          switchMap(popups => {
            if (!popups || popups.length === 0) {
              return of(false);
            }

            // Use the same date format as PopupTriggerService
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            const unshownPopups = popups.filter(
              popup =>
                !this.storageService.wasPopupShownToday(
                  popup.popupId,
                  branchId,
                  today
                ) &&
                !this.storageService.isDontShowAgain(popup.popupId, branchId) &&
                popup.canBeDisplayed()
            );

            return of(unshownPopups.length > 0);
          })
        );
      })
    );
  }
}
