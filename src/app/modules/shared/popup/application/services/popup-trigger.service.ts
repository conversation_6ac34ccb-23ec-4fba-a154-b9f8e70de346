import { Injectable, inject } from '@angular/core';
import { Observable, of, switchMap, map } from 'rxjs';
import { PopupRepository } from '../../infra/repositories/popup.repository';
import { PopupStorageService } from './popup-storage.service';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from '../usecases/show-popup.usecase';

export interface PopupTriggerConfig {
  branchId: number;
  checkPaymentSuccess?: boolean;
  checkLoginSuccess?: boolean;
}

/**
 * Service that orchestrates when and how to display popups.
 * Coordinates the business logic to determine if a popup should be shown.
 */
@Injectable({ providedIn: 'root' })
export class PopupTriggerService {
  private popupRepository = inject(PopupRepository);
  private popupStorage = inject(PopupStorageService);
  private showPopupUseCase = inject(ShowPopupUseCase);

  /**
   * Checks if there are available popups and if they should be shown.
   * @param config - Trigger configuration
   * @returns Observable that emits true if a popup should be shown
   */
  shouldShowPopup(config: PopupTriggerConfig): Observable<boolean> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    return this.popupRepository
      .getAvailablePopups(Number(config.branchId))
      .pipe(
        switchMap(popups => {
          if (popups.length === 0) {
            return of(false);
          }

          // Sort by priority (higher number = higher priority)
          const sortedPopups = popups.sort((a, b) => b.priority - a.priority);

          // Find the first popup that hasn't been shown today AND isn't marked as "Don't show again"
          for (const popup of sortedPopups) {
            const wasShown = this.popupStorage.wasPopupShownToday(
              popup.popupId,
              Number(config.branchId),
              today
            );
            const isDontShowAgain = this.popupStorage.isDontShowAgain(
              popup.popupId,
              Number(config.branchId)
            );

            if (!wasShown && !isDontShowAgain) {
              return of(true);
            }
          }

          return of(false);
        })
      );
  }

  /**
   * Executes the complete logic of showing a popup if appropriate.
   * @param config - Trigger configuration
   * @returns Observable that emits the operation result
   */
  triggerPopupIfNeeded(config: PopupTriggerConfig): Observable<boolean> {
    return this.shouldShowPopup(config).pipe(
      switchMap(shouldShow => {
        if (!shouldShow) {
          return of(false);
        }

        return this.showPopupUseCase.execute().pipe(
          map((result: ShowPopupResult) => {
            return result.success;
          })
        );
      })
    );
  }

  /**
   * Specific trigger for after successful login.
   * @param branchId - Branch ID
   * @returns Observable that emits the result
   */
  triggerAfterLogin(branchId: number): Observable<boolean> {
    return this.triggerPopupIfNeeded({
      branchId,
      checkLoginSuccess: true,
    });
  }

  /**
   * Specific trigger for after successful payment.
   * @param branchId - Branch ID
   * @returns Observable that emits the result
   */
  triggerAfterPaymentSuccess(branchId: number): Observable<boolean> {
    return this.triggerPopupIfNeeded({
      branchId,
      checkPaymentSuccess: true,
    });
  }

  /**
   * Cleans up old records of shown popups.
   */
  cleanupOldRecords(): void {
    this.popupStorage.cleanupOldRecords();
  }

  /**
   * Gets debug information about the popup state for a branch.
   * @param branchId - Branch ID
   * @returns Observable with debug information
   */
  getDebugInfo(branchId: number): Observable<{
    availablePopups: any[];
    shownToday: any[];
    dontShowAgain: any[];
    eligiblePopups: any[];
  }> {
    const today = new Date().toISOString().split('T')[0];

    return this.popupRepository.getAvailablePopups(branchId).pipe(
      map(popups => {
        const shownToday = popups.filter(popup =>
          this.popupStorage.wasPopupShownToday(popup.popupId, branchId, today)
        );

        const dontShowAgain = popups.filter(popup =>
          this.popupStorage.isDontShowAgain(popup.popupId, branchId)
        );

        const eligiblePopups = popups.filter(
          popup =>
            !this.popupStorage.wasPopupShownToday(
              popup.popupId,
              branchId,
              today
            ) && !this.popupStorage.isDontShowAgain(popup.popupId, branchId)
        );

        return {
          availablePopups: popups,
          shownToday,
          dontShowAgain,
          eligiblePopups,
        };
      })
    );
  }
}
