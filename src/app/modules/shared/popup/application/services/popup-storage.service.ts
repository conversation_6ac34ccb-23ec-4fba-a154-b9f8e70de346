import { Injectable } from '@angular/core';

interface PopupShownRecord {
  popupId: number;
  branchId: number;
  date: string;
  timestamp: number;
}

interface DontShowAgainRecord {
  popupId: number;
  branchId: number;
  timestamp: number;
}

/**
 * Servicio para gestionar el estado de popups mostrados.
 * Almacena información sobre qué popups han sido mostrados por branch y fecha.
 */
@Injectable({ providedIn: 'root' })
export class PopupStorageService {
  private readonly STORAGE_KEY = 'aplazo_popups_shown';
  private readonly DONT_SHOW_AGAIN_KEY = 'aplazo_popups_dont_show_again';

  /**
   * Verifica si un popup fue mostrado hoy para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @param date - Fecha en formato string (YYYY-MM-DD)
   * @returns True si el popup fue mostrado hoy
   */
  wasPopupShownToday(popupId: number, branchId: number, date: string): boolean {
    const records = this.getShownRecordsInternal();
    return records.some(
      record =>
        record.popupId === popupId &&
        record.branchId === branchId &&
        record.date === date
    );
  }

  /**
   * Verifica si un popup está marcado como "No mostrar de nuevo" para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @returns True si el popup está marcado como "No mostrar de nuevo"
   */
  isDontShowAgain(popupId: number, branchId: number): boolean {
    const records = this.getDontShowAgainRecordsInternal();
    return records.some(
      record => record.popupId === popupId && record.branchId === branchId
    );
  }

  /**
   * Marca un popup como "No mostrar de nuevo" para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   */
  saveDontShowAgain(popupId: number, branchId: number): void {
    const records = this.getDontShowAgainRecordsInternal();

    // Evitar duplicados
    const exists = records.some(
      record => record.popupId === popupId && record.branchId === branchId
    );

    if (!exists) {
      const newRecord: DontShowAgainRecord = {
        popupId,
        branchId,
        timestamp: Date.now(),
      };

      records.push(newRecord);
      this.saveDontShowAgainRecords(records);
    }
  }

  /**
   * Marca un popup como mostrado para una branch y fecha específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @param date - Fecha en formato string (YYYY-MM-DD)
   */
  markPopupAsShown(popupId: number, branchId: number, date: string): void {
    const records = this.getShownRecordsInternal();

    // Evitar duplicados
    const exists = records.some(
      record =>
        record.popupId === popupId &&
        record.branchId === branchId &&
        record.date === date
    );

    if (!exists) {
      const newRecord: PopupShownRecord = {
        popupId,
        branchId,
        date,
        timestamp: Date.now(),
      };

      records.push(newRecord);
      this.saveShownRecords(records);
    }
  }

  /**
   * Limpia registros antiguos (más de 30 días).
   */
  cleanupOldRecords(): void {
    const records = this.getShownRecordsInternal();
    const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;

    const filteredRecords = records.filter(
      record => record.timestamp > thirtyDaysAgo
    );

    if (filteredRecords.length !== records.length) {
      this.saveShownRecords(filteredRecords);
    }
  }

  /**
   * Obtiene todos los registros de popups mostrados.
   * @returns Array de registros
   */
  private getShownRecordsInternal(): PopupShownRecord[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Error reading popup storage:', error);
      return [];
    }
  }

  /**
   * Obtiene todos los registros de "No mostrar de nuevo".
   * @returns Array de registros
   */
  private getDontShowAgainRecordsInternal(): DontShowAgainRecord[] {
    try {
      const stored = localStorage.getItem(this.DONT_SHOW_AGAIN_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Error reading dont show again storage:', error);
      return [];
    }
  }

  /**
   * Obtiene todos los registros de popups mostrados (método público para demo).
   * @returns Array de registros
   */
  getShownRecords(): PopupShownRecord[] {
    return this.getShownRecordsInternal();
  }

  /**
   * Obtiene todos los registros de "No mostrar de nuevo" (método público para demo).
   * @returns Array de registros
   */
  getDontShowAgainRecords(): DontShowAgainRecord[] {
    return this.getDontShowAgainRecordsInternal();
  }

  /**
   * Guarda los registros de popups mostrados.
   * @param records - Array de registros a guardar
   */
  private saveShownRecords(records: PopupShownRecord[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(records));
    } catch (error) {
      console.warn('Error saving popup storage:', error);
    }
  }

  /**
   * Guarda los registros de "No mostrar de nuevo".
   * @param records - Array de registros a guardar
   */
  private saveDontShowAgainRecords(records: DontShowAgainRecord[]): void {
    try {
      localStorage.setItem(this.DONT_SHOW_AGAIN_KEY, JSON.stringify(records));
    } catch (error) {
      console.warn('Error saving dont show again storage:', error);
    }
  }

  /**
   * Limpia todos los registros (útil para testing).
   */
  clearAllRecords(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.DONT_SHOW_AGAIN_KEY);
  }

  /**
   * Limpia registros para una fecha específica (útil para testing).
   * @param date - Fecha en formato YYYY-MM-DD
   */
  clearRecordsForDate(date: string): void {
    const records = this.getShownRecords();
    const filteredRecords = records.filter(record => record.date !== date);
    this.saveShownRecords(filteredRecords);
  }

  /**
   * Limpia registros de "No mostrar de nuevo" para una branch específica (útil para testing).
   * @param branchId - ID de la branch
   */
  clearDontShowAgainRecordsForBranch(branchId: number): void {
    const records = this.getDontShowAgainRecords();
    const filteredRecords = records.filter(
      record => record.branchId !== branchId
    );
    this.saveDontShowAgainRecords(filteredRecords);
  }
}
